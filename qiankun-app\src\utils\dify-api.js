// Dify API 服务
const DIFY_API_URL = 'http://49cw9260ms55.vicp.fun/v1'; // 根据提供的API案例修改URL
// 使用真实API响应，不使用模拟响应
const USE_MOCK_RESPONSE = false;
const DIFY_API_KEY ='app-pTsif9ip7FyzAa2eDMeNAa1d'  //'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhMGY0YmFhNC1mN2JlLTQyYjItYjc2ZS0xMzZkM2U5YzAyZGMiLCJzdWIiOiJXZWIgQVBJIFBhc3Nwb3J0IiwiYXBwX2lkIjoiYTBmNGJhYTQtZjdiZS00MmIyLWI3NmUtMTM2ZDNlOWMwMmRjIiwiYXBwX2NvZGUiOiJQWHF2bDZENUpVZ3RkSnhWIiwiZW5kX3VzZXJfaWQiOiIzY2JlMTQ4Yi1lZDI3LTQxZTEtOGIwNy00NGUwOWZlODFmOTQifQ.HvT621B0rFr5l28skljJ1npxZTwokmetdT62N2lVM_w'
// 'app-pTsif9ip7FyzAa2eDMeNAa1d'//
export const difyApi = {
  // 发送消息到Dify API
  sendMessage(message, conversationId = null) {
    return new Promise((resolve, reject) => {
      // 如果使用模拟响应
      if (USE_MOCK_RESPONSE) {
        console.log('使用模拟响应');
        // 延迟1.5秒，模拟网络请求
        setTimeout(() => {
          // 生成一个随机的会话ID（如果没有提供）
          const newConversationId = conversationId || this.generateUUID();

          // 模拟API响应
          const mockResponse = {
            conversation_id: newConversationId,
            answer: this.generateMockResponse(message),
            created_at: new Date().toISOString()
          };

          console.log('模拟响应:', mockResponse);
          resolve(mockResponse);
        }, 1500);
        return;
      }

      // 获取当前平台
      const platform = uni.getSystemInfoSync().platform;
      console.log('当前平台:', platform);

      // 小程序环境和H5环境使用不同的请求方式
      if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
        // 在小程序环境中，直接使用 uni.request，不受同源策略限制
        console.log('in小程序')
        uni.request({
          url: `${DIFY_API_URL}/chat-messages`,
          method: 'POST',
          timeout: 30000000, // 30秒超时
          header: {
            'authorization': `Bearer ${DIFY_API_KEY}`,
            'Content-Type': 'application/json',
            "Cookie":"locale=zh-Hans; remember_user_token=eyJfcmFpbHMiOnsibWVzc2FnZSI6Ilcxc3pYU3dpSkRKaEpERXdKRk5tUmtwclJFODVMME5qYjBaYU1sa3ZUMlZrTGs4aUxDSXhOelEyTkRrMU16VTFMamswT1RreE9EY2lYUT09IiwiZXhwIjoiMjAyNS0wNS0yMFQwMTozNTo1NS45NDlaIiwicHVyIjoiY29va2llLnJlbWVtYmVyX3VzZXJfdG9rZW4ifX0%3D--5a94194c3acc586c258ef8e4e27038795a98b284; _gitlab_session=3ca5a8079dae51322c99bb0493a8e14e"
          },
          data: {
            files:[],
            parent_message_id:"86176299-a69b-470c-b5b8-3572bfa24dc2",
            inputs: {},
            query: message,
            response_mode: 'blocking', // 或者使用 'streaming'
            conversation_id: '',//conversationId || '',
            user: uni.getStorageSync('userInfo')?.username || 'anonymous'
          },
          success: (res) => {
            console.log('Dify API 响应状态码:', res.statusCode);
            console.log('Dify API 响应头:', res.header);
            console.log('Dify API 响应数据:', res.data);

            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              console.error('API请求失败，状态码:', res.statusCode);
              console.error('错误响应数据:', res.data);
              reject(new Error(`API请求失败: ${res.statusCode}`));
            }
          },
          fail: (err) => {
            console.error('API请求失败，错误信息:', err);
            reject(err);
          }
        });
      } else {
        console.log('inH5')
        // 在H5环境中，使用代理服务器解决跨域问题
        uni.request({
          url: `http://49cw9260ms55.vicp.fun/v1/chat-messages`, // 使用相对路径，通过代理访问
          method: 'POST',
          timeout: 30000, // 30秒超时
          header: {
            'Authorization': `Bearer ${DIFY_API_KEY}`,
            'Content-Type': 'application/json'
          },
          data: {
            inputs: {},
            query: message,
            response_mode: 'blocking', // 或者使用 'streaming'
            conversation_id: '',//conversationId || '',
            user: uni.getStorageSync('userInfo')?.username || 'anonymous'
          },
          success: (res) => {
            console.log('Dify API 响应状态码:', res.statusCode);
            console.log('Dify API 响应头:', res.header);
            console.log('Dify API 响应数据:', res.data);

            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              console.error('API请求失败，状态码:', res.statusCode);
              console.error('错误响应数据:', res.data);
              reject(new Error(`API请求失败: ${res.statusCode}`));
            }
          },
          fail: (err) => {
            console.error('API请求失败，错误信息:', err);
            reject(err);
          }
        });
      }
    });
  },

  // 生成模拟响应
  generateMockResponse(message) {
    const responses = [
      `您好，我是乾坤袋AI助手。您的问题是关于"${message}"，我来为您解答：\n\n这是一个很好的问题。根据我的理解，这涉及到几个方面：\n\n1. 首先，我们需要考虑数据的准确性和完整性\n2. 其次，重要的是分析方法的选择\n3. 最后，不要忘记结果的验证和解释\n\n希望这个回答对您有所帮助！`,
      `感谢您的提问："${message}"\n\n## 分析\n\n这个问题可以从多个角度来看：\n\n* 从技术层面：需要考虑实现的可行性\n* 从业务层面：需要评估投资回报率\n* 从用户体验层面：需要确保易用性和满意度\n\n## 建议\n\n我建议您可以尝试以下方法：\n\n\`\`\`\n1. 步骤一：分析需求和现状\n2. 步骤二：制定详细计划\n3. 步骤三：执行并持续评估\n\`\`\`\n\n如果您有更多问题，随时可以继续提问！`,
      `关于"${message}"，我有以下见解：\n\n> 这是一个复杂的话题，需要综合考虑多方面因素。\n\n主要包括：\n\n* 第一点：市场趋势和竞争分析\n* 第二点：资源配置和优先级\n* 第三点：风险管理和应对策略\n\n您可以参考最新的行业报告和专家观点，进一步深入了解。`
    ];

    // 随机选择一个回复
    return responses[Math.floor(Math.random() * responses.length)];
  },

  // 生成UUID
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  // 获取历史对话
  getConversations() {
    return new Promise((resolve, reject) => {
      // 获取当前平台
      const platform = uni.getSystemInfoSync().platform;

      // 根据平台选择不同的请求URL
      let requestUrl = '';

      // 小程序环境使用完整URL，H5环境使用相对URL（通过代理）
      if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
        requestUrl = `${DIFY_API_URL}/conversations`;
      } else {
        requestUrl = '/v1/conversations';
      }

      uni.request({
        url: requestUrl,
        method: 'GET',
        timeout: 30000, // 30秒超时
        header: {
          'Authorization': `Bearer ${DIFY_API_KEY}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`API请求失败: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  // 获取特定对话的消息
  getMessages(conversationId) {
    return new Promise((resolve, reject) => {
      // 如果使用模拟响应
      if (USE_MOCK_RESPONSE) {
        console.log('使用模拟响应获取消息');
        // 延迟1秒，模拟网络请求
        setTimeout(() => {
          // 从本地存储获取对话
          const conversations = uni.getStorageSync('conversations') || [];
          const conversation = conversations.find(c => c.id === conversationId);

          if (conversation) {
            // 转换为API响应格式
            const messages = conversation.messages.map(msg => ({
              role: msg.role,
              content: msg.content,
              created_at: new Date().toISOString()
            }));

            resolve({ data: messages });
          } else {
            // 如果找不到对话，返回空数组
            resolve({ data: [] });
          }
        }, 1000);
        return;
      }

      // 获取当前平台
      const platform = uni.getSystemInfoSync().platform;

      // 根据平台选择不同的请求URL
      let requestUrl = '';

      // 小程序环境使用完整URL，H5环境使用相对URL（通过代理）
      if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
        requestUrl = `${DIFY_API_URL}/conversations/${conversationId}/messages`;
      } else {
        requestUrl = `/v1/conversations/${conversationId}/messages`;
      }

      // 真实API调用
      uni.request({
        url: requestUrl,
        method: 'GET',
        timeout: 30000, // 30秒超时
        header: {
          'Authorization': `Bearer ${DIFY_API_KEY}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`API请求失败: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
};
