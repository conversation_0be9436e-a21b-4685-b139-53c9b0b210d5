<template>
  <view class="scan-container">
    <view class="header">
      <text class="title">扫描业务人员二维码</text>
      <text class="subtitle">您需要扫描业务人员的二维码才能使用乾坤袋</text>
    </view>
    
    <view class="scanner-area">
      <view class="scanner-box">
        <!-- 在微信小程序中使用 camera 组件 -->
        <!-- #ifdef MP-WEIXIN -->
        <camera 
          class="camera" 
          mode="scanCode" 
          @scancode="onScanCode" 
          :flash="flashMode"
        ></camera>
        <!-- #endif -->
        
        <!-- 在H5中使用自定义扫码组件 -->
        <!-- #ifdef H5 -->
        <view class="h5-scanner">
          <text class="scanner-tip">H5环境下请点击下方按钮上传二维码图片</text>
        </view>
        <!-- #endif -->
        
        <view class="scan-frame"></view>
      </view>
      
      <view class="scanner-actions">
        <button class="action-btn" @click="toggleFlash">
          <text>{{ flashMode === 'on' ? '关闭闪光灯' : '打开闪光灯' }}</text>
        </button>
        
        <!-- 在H5中提供上传图片按钮 -->
        <!-- #ifdef H5 -->
        <button class="action-btn" @click="uploadQRCode">
          <text>上传二维码图片</text>
        </button>
        <!-- #endif -->
      </view>
    </view>
    
    <view class="manual-input">
      <text class="manual-text">无法扫描？</text>
      <text class="manual-link" @click="showManualInput">手动输入业务人员编号</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      flashMode: 'off',
      isScanning: true
    }
  },
  onLoad() {
    // 检查相机权限
    // #ifdef MP-WEIXIN
    uni.authorize({
      scope: 'scope.camera',
      success: () => {
        console.log('相机权限已授权');
      },
      fail: () => {
        uni.showModal({
          title: '提示',
          content: '需要相机权限才能扫描二维码',
          showCancel: false,
          success: () => {
            uni.openSetting();
          }
        });
      }
    });
    // #endif
  },
  methods: {
    onScanCode(e) {
      if (!this.isScanning) return;
      
      this.isScanning = false;
      
      // 获取扫描结果
      const result = e.detail.result;
      
      // 验证二维码格式
      try {
        // 假设二维码内容是JSON格式，包含业务人员信息
        const businessPersonnel = JSON.parse(result);
        
        if (!businessPersonnel.id || !businessPersonnel.name) {
          throw new Error('无效的业务人员二维码');
        }
        
        // 保存业务人员信息
        uni.setStorageSync('businessPersonnel', businessPersonnel);
        
        // 显示成功提示
        uni.showToast({
          title: '绑定成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 跳转到消息列表页
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/chat/chat-list'
              });
            }, 2000);
          }
        });
      } catch (error) {
        uni.showModal({
          title: '扫描失败',
          content: '无效的业务人员二维码，请重新扫描',
          showCancel: false,
          success: () => {
            this.isScanning = true;
          }
        });
      }
    },
    
    toggleFlash() {
      this.flashMode = this.flashMode === 'on' ? 'off' : 'on';
    },
    
    uploadQRCode() {
      // #ifdef H5
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 这里应该有一个解析图片中二维码的逻辑
          // 由于H5环境下无法直接解析二维码，这里模拟一个成功的结果
          
          // 模拟业务人员信息
          const businessPersonnel = {
            id: 'BP123456',
            name: '张业务',
            department: '销售部',
            phone: '***********'
          };
          
          // 保存业务人员信息
          uni.setStorageSync('businessPersonnel', businessPersonnel);
          
          // 显示成功提示
          uni.showToast({
            title: '绑定成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              // 跳转到消息列表页
              setTimeout(() => {
                uni.switchTab({
                  url: '/pages/chat/chat-list'
                });
              }, 2000);
            }
          });
        }
      });
      // #endif
    },
    
    showManualInput() {
      uni.showModal({
        title: '输入业务人员编号',
        editable: true,
        placeholderText: '请输入业务人员编号',
        success: (res) => {
          if (res.confirm && res.content) {
            const businessPersonnelId = res.content.trim();
            
            if (businessPersonnelId) {
              // 这里应该有一个验证业务人员编号的API调用
              // 模拟业务人员信息
              const businessPersonnel = {
                id: businessPersonnelId,
                name: '张业务',
                department: '销售部',
                phone: '***********'
              };
              
              // 保存业务人员信息
              uni.setStorageSync('businessPersonnel', businessPersonnel);
              
              // 显示成功提示
              uni.showToast({
                title: '绑定成功',
                icon: 'success',
                duration: 2000,
                success: () => {
                  // 跳转到消息列表页
                  setTimeout(() => {
                    uni.switchTab({
                      url: '/pages/chat/chat-list'
                    });
                  }, 2000);
                }
              });
            } else {
              uni.showToast({
                title: '请输入有效的业务人员编号',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
}
</script>

<style>
.scan-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  background-color: #000;
}

.header {
  padding: 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.scanner-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.scanner-box {
  width: 600rpx;
  height: 600rpx;
  position: relative;
  margin-bottom: 60rpx;
}

.camera, .h5-scanner {
  width: 100%;
  height: 100%;
  background-color: #333;
}

.h5-scanner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.scanner-tip {
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}

.scan-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4rpx solid #007AFF;
  box-sizing: border-box;
}

.scan-frame::before, .scan-frame::after {
  content: '';
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border-color: #007AFF;
  border-style: solid;
}

.scan-frame::before {
  top: -4rpx;
  left: -4rpx;
  border-width: 4rpx 0 0 4rpx;
}

.scan-frame::after {
  bottom: -4rpx;
  right: -4rpx;
  border-width: 0 4rpx 4rpx 0;
}

.scanner-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.action-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 45rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
  padding: 0 40rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.manual-input {
  margin-bottom: 60rpx;
  display: flex;
  align-items: center;
}

.manual-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

.manual-link {
  font-size: 28rpx;
  color: #007AFF;
  margin-left: 10rpx;
}
</style>
