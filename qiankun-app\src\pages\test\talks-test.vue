<template>
  <view class="page-container">
    <view class="card">
      <view class="title">对话历史测试</view>
      
      <view class="info-section">
        <text class="section-title">当前用户信息</text>
        <view class="info-item">
          <text class="info-label">用户ID:</text>
          <text class="info-value">{{ currentUser.id || '未登录' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用户名:</text>
          <text class="info-value">{{ currentUser.name || '未知' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用户级别:</text>
          <text class="info-value">{{ currentUser.level || '未知' }} ({{ getLevelName(currentUser.level) }})</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="test-section">
        <text class="section-title">测试功能</text>
        
        <button class="test-btn" @click="testGetTalks" :disabled="isLoading">
          <text v-if="!isLoading">测试获取对话</text>
          <text v-else>获取中...</text>
        </button>
        
        <button class="test-btn" @click="goToHistoryPage">
          <text>跳转到历史记录页面</text>
        </button>
      </view>
      
      <view class="divider"></view>
      
      <view class="result-section">
        <text class="section-title">API响应结果</text>
        <view class="result-container">
          <text class="result-text">{{ responseText }}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="data-section" v-if="talksData.length > 0">
        <text class="section-title">对话数据 ({{ talksData.length }}条)</text>
        <view class="data-list">
          <view 
            class="data-item" 
            v-for="(talk, index) in talksData.slice(0, 5)" 
            :key="index"
          >
            <view class="data-header">
              <text class="user-name">{{ talk.name }}</text>
              <text class="user-level">{{ getLevelName(talk.level) }}</text>
              <text class="talk-date">{{ formatDate(talk.date) }}</text>
            </view>
            <view class="data-content">
              <text class="question">Q: {{ talk.question }}</text>
              <text class="answer">A: {{ talk.answer }}</text>
            </view>
          </view>
          
          <view v-if="talksData.length > 5" class="more-data">
            <text class="more-text">还有 {{ talksData.length - 5 }} 条数据...</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'
import { getUserInfo } from '@/utils/auth.js'

export default {
  data() {
    return {
      currentUser: {},
      isLoading: false,
      responseText: '暂无响应',
      talksData: []
    }
  },
  onLoad() {
    this.currentUser = getUserInfo() || {};
  },
  methods: {
    // 测试获取对话
    async testGetTalks() {
      if (!this.currentUser.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }
      
      this.isLoading = true;
      this.responseText = '请求中...';
      
      try {
        console.log('开始测试GetTalks接口，用户ID:', this.currentUser.id);
        
        const response = await userApi.getTalks(this.currentUser.id);
        
        console.log('GetTalks响应:', response);
        
        this.responseText = JSON.stringify(response, null, 2);
        
        if (response.code === 200 && response.data) {
          this.talksData = response.data;
          
          uni.showToast({
            title: `获取成功，共${response.data.length}条对话`,
            icon: 'success'
          });
        } else if (response.code === 203) {
          uni.showToast({
            title: '超级管理员无法查看对话记录',
            icon: 'none'
          });
        } else if (response.code === 204) {
          this.talksData = [];
          uni.showToast({
            title: '暂无对话记录',
            icon: 'none'
          });
        } else {
          throw new Error(`API返回错误: ${response.code}`);
        }
      } catch (error) {
        console.error('测试GetTalks失败:', error);
        
        this.responseText = `错误: ${error.message}`;
        
        uni.showToast({
          title: error.message || '获取失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 跳转到历史记录页面
    goToHistoryPage() {
      uni.switchTab({
        url: '/pages/chat/chat-history'
      });
    },
    
    // 获取级别名称
    getLevelName(level) {
      switch (level) {
        case 1:
          return '超级管理员';
        case 2:
          return '客户经理';
        case 3:
        default:
          return '普通用户';
      }
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知时间';
      
      const date = new Date(dateString);
      const now = new Date();
      
      // 如果是今天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      }
      
      // 如果是昨天的消息，显示"昨天"
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      }
      
      // 其他情况显示完整日期
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  }
}
</script>

<style>
.info-section, .test-section, .result-section, .data-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn:active {
  background: #0056CC;
}

.test-btn:disabled {
  background: #ccc;
}

.result-container {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-text {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
}

.data-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.data-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border: 1rpx solid #e5e5e5;
}

.data-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.user-level {
  font-size: 22rpx;
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}

.talk-date {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

.data-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.question, .answer {
  font-size: 26rpx;
  line-height: 1.5;
  padding: 10rpx;
  border-radius: 8rpx;
}

.question {
  background: #E3F2FD;
  color: #1976D2;
}

.answer {
  background: #F5F5F5;
  color: #333;
}

.more-data {
  text-align: center;
  padding: 20rpx;
}

.more-text {
  font-size: 26rpx;
  color: #666;
}
</style>
