/* 全局样式 */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.btn-primary {
  background-color: #007AFF;
  color: #fff;
}

.btn-secondary {
  background-color: #fff;
  color: #007AFF;
  border: 1px solid #007AFF;
}

.btn-success {
  background-color: #4CD964;
  color: #fff;
}

.btn-danger {
  background-color: #FF3B30;
  color: #fff;
}

.btn-warning {
  background-color: #FF9500;
  color: #fff;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  height: 90rpx;
  border-bottom: 1px solid #e5e5e5;
  font-size: 32rpx;
  width: 100%;
}

/* 头像样式 */
.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: #f5f5f5;
  margin: 20rpx 0;
}

/* 徽章 */
.badge {
  min-width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  background-color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
}

.badge-text {
  font-size: 24rpx;
  color: #fff;
}

/* 状态标签 */
.status-tag {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-success {
  color: #4CD964;
  background-color: rgba(76, 217, 100, 0.1);
}

.status-warning {
  color: #FF9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.status-danger {
  color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.1);
}

/* 链接样式 */
.link {
  color: #007AFF;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}
