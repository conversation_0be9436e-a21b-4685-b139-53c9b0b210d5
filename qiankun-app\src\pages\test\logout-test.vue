<template>
  <view class="page-container">
    <view class="card">
      <view class="title">退出登录测试</view>
      
      <view class="info-section">
        <text class="section-title">当前登录状态</text>
        <view class="info-item">
          <text class="info-label">Token:</text>
          <text class="info-value">{{ currentToken || '未登录' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用户信息:</text>
          <text class="info-value">{{ currentUser ? currentUser.name : '未登录' }}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="test-section">
        <text class="section-title">测试功能</text>
        
        <!-- 使用LogoutButton组件 -->
        <LogoutButton 
          buttonText="测试退出登录"
          buttonClass="test-logout-btn"
          @logout-success="onLogoutSuccess"
          @logout-error="onLogoutError"
          @before-redirect="onBeforeRedirect"
          @redirect-success="onRedirectSuccess"
          @redirect-error="onRedirectError"
        />
        
        <!-- 直接调用API测试 -->
        <button class="test-btn" @click="testLogoutApi">
          <text>直接测试退出API</text>
        </button>
        
        <!-- 清空缓存测试 -->
        <button class="test-btn" @click="testClearCache">
          <text>测试清空缓存</text>
        </button>
        
        <!-- 刷新状态 -->
        <button class="test-btn" @click="refreshStatus">
          <text>刷新状态</text>
        </button>
      </view>
      
      <view class="divider"></view>
      
      <view class="log-section">
        <text class="section-title">操作日志</text>
        <view class="log-container">
          <text 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
          >
            {{ log }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import LogoutButton from '@/components/LogoutButton.vue'
import { userApi } from '@/utils/api.js'
import { clearAllCache } from '@/utils/auth.js'

export default {
  components: {
    LogoutButton
  },
  data() {
    return {
      currentToken: '',
      currentUser: null,
      logs: []
    }
  },
  onLoad() {
    this.refreshStatus();
  },
  methods: {
    // 刷新当前状态
    refreshStatus() {
      this.currentToken = uni.getStorageSync('token');
      this.currentUser = uni.getStorageSync('userInfo');
      this.addLog('状态已刷新');
    },
    
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      
      // 限制日志数量
      if (this.logs.length > 10) {
        this.logs = this.logs.slice(0, 10);
      }
    },
    
    // 退出登录成功事件
    onLogoutSuccess(result) {
      this.addLog(`退出登录成功: ${result.message}`);
      this.refreshStatus();
    },
    
    // 退出登录失败事件
    onLogoutError(error) {
      this.addLog(`退出登录失败: ${error.message}`);
    },
    
    // 跳转前事件
    onBeforeRedirect() {
      this.addLog('准备跳转到登录页');
    },
    
    // 跳转成功事件
    onRedirectSuccess() {
      this.addLog('跳转成功');
    },
    
    // 跳转失败事件
    onRedirectError(error) {
      this.addLog(`跳转失败: ${error.message}`);
    },
    
    // 直接测试退出API
    async testLogoutApi() {
      try {
        this.addLog('开始测试退出API');
        
        const response = await userApi.logout();
        this.addLog(`API响应: ${JSON.stringify(response)}`);
        
        uni.showToast({
          title: 'API测试成功',
          icon: 'success'
        });
      } catch (error) {
        this.addLog(`API测试失败: ${error.message}`);
        
        uni.showToast({
          title: 'API测试失败',
          icon: 'none'
        });
      }
    },
    
    // 测试清空缓存
    testClearCache() {
      try {
        this.addLog('开始测试清空缓存');
        
        const result = clearAllCache();
        this.addLog(`清空缓存结果: ${result ? '成功' : '失败'}`);
        
        this.refreshStatus();
        
        uni.showToast({
          title: '缓存已清空',
          icon: 'success'
        });
      } catch (error) {
        this.addLog(`清空缓存失败: ${error.message}`);
        
        uni.showToast({
          title: '清空缓存失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style>
.info-section, .test-section, .log-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.test-logout-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn:active {
  background: #0056CC;
}

.log-container {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  display: block;
  line-height: 1.5;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.log-item:last-child {
  margin-bottom: 0;
}
</style>
