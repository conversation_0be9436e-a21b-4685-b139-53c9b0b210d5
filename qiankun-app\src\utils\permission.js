/**
 * 权限管理工具类
 * 处理iOS和Android平台的权限请求
 */

// 请求相机权限
export const requestCameraPermission = () => {
  return new Promise((resolve, reject) => {
    // #ifdef APP-PLUS
    const platform = uni.getSystemInfoSync().platform;
    
    if (platform === 'ios') {
      // iOS权限处理
      plus.ios.requestAuthorization('camera', (result) => {
        if (result === 'authorized') {
          resolve(true);
        } else {
          reject(new Error('相机权限被拒绝'));
        }
      });
    } else if (platform === 'android') {
      // Android权限处理
      const main = plus.android.runtimeMainActivity();
      const pkgName = main.getPackageName();
      const uid = main.getApplicationInfo().uid;
      
      const PermissionChecker = plus.android.importClass('android.support.v4.content.PermissionChecker');
      const permission = 'android.permission.CAMERA';
      
      const result = PermissionChecker.checkSelfPermission(main, permission);
      if (result === PermissionChecker.PERMISSION_GRANTED) {
        resolve(true);
      } else {
        // 请求权限
        plus.android.requestPermissions([permission], (resultObj) => {
          if (resultObj.granted && resultObj.granted.length > 0) {
            resolve(true);
          } else {
            reject(new Error('相机权限被拒绝'));
          }
        });
      }
    }
    // #endif
    
    // #ifdef MP-WEIXIN || H5
    // 小程序和H5环境直接返回成功
    resolve(true);
    // #endif
  });
};

// 请求相册权限
export const requestAlbumPermission = () => {
  return new Promise((resolve, reject) => {
    // #ifdef APP-PLUS
    const platform = uni.getSystemInfoSync().platform;
    
    if (platform === 'ios') {
      // iOS相册权限
      plus.ios.requestAuthorization('photos', (result) => {
        if (result === 'authorized') {
          resolve(true);
        } else {
          reject(new Error('相册权限被拒绝'));
        }
      });
    } else if (platform === 'android') {
      // Android存储权限
      const main = plus.android.runtimeMainActivity();
      const PermissionChecker = plus.android.importClass('android.support.v4.content.PermissionChecker');
      const permission = 'android.permission.READ_EXTERNAL_STORAGE';
      
      const result = PermissionChecker.checkSelfPermission(main, permission);
      if (result === PermissionChecker.PERMISSION_GRANTED) {
        resolve(true);
      } else {
        // 请求权限
        plus.android.requestPermissions([permission], (resultObj) => {
          if (resultObj.granted && resultObj.granted.length > 0) {
            resolve(true);
          } else {
            reject(new Error('相册权限被拒绝'));
          }
        });
      }
    }
    // #endif
    
    // #ifdef MP-WEIXIN || H5
    // 小程序和H5环境直接返回成功
    resolve(true);
    // #endif
  });
};

// 检查并请求相机权限
export const checkAndRequestCameraPermission = async () => {
  try {
    await requestCameraPermission();
    return true;
  } catch (error) {
    console.error('相机权限请求失败:', error);
    
    uni.showModal({
      title: '权限提示',
      content: '需要相机权限才能拍照，请在设置中开启相机权限',
      showCancel: false,
      confirmText: '我知道了'
    });
    
    return false;
  }
};

// 检查并请求相册权限
export const checkAndRequestAlbumPermission = async () => {
  try {
    await requestAlbumPermission();
    return true;
  } catch (error) {
    console.error('相册权限请求失败:', error);
    
    uni.showModal({
      title: '权限提示',
      content: '需要相册权限才能选择图片，请在设置中开启相册权限',
      showCancel: false,
      confirmText: '我知道了'
    });
    
    return false;
  }
};

// 统一的图片选择方法
export const chooseImageWithPermission = (options = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const defaultOptions = {
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      };
      
      const finalOptions = { ...defaultOptions, ...options };
      
      // 如果包含相机选项，检查相机权限
      if (finalOptions.sourceType.includes('camera')) {
        const cameraPermission = await checkAndRequestCameraPermission();
        if (!cameraPermission) {
          // 移除相机选项
          finalOptions.sourceType = finalOptions.sourceType.filter(type => type !== 'camera');
        }
      }
      
      // 如果包含相册选项，检查相册权限
      if (finalOptions.sourceType.includes('album')) {
        const albumPermission = await checkAndRequestAlbumPermission();
        if (!albumPermission) {
          // 移除相册选项
          finalOptions.sourceType = finalOptions.sourceType.filter(type => type !== 'album');
        }
      }
      
      // 如果没有可用的选项，返回错误
      if (finalOptions.sourceType.length === 0) {
        reject(new Error('没有可用的图片来源'));
        return;
      }
      
      // 调用uni.chooseImage
      uni.chooseImage({
        ...finalOptions,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
      
    } catch (error) {
      reject(error);
    }
  });
};

// 压缩图片
export const compressImage = (filePath, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: quality,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (err) => {
        console.error('图片压缩失败:', err);
        // 如果压缩失败，返回原图片
        resolve(filePath);
      }
    });
  });
};
