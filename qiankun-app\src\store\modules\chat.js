export default {
  namespaced: true,
  state: {
    chatList: [],
    currentChat: null,
    messages: []
  },
  mutations: {
    SET_CHAT_LIST(state, chatList) {
      state.chatList = chatList;
    },
    SET_CURRENT_CHAT(state, chat) {
      state.currentChat = chat;
    },
    SET_MESSAGES(state, messages) {
      state.messages = messages;
    },
    ADD_MESSAGE(state, message) {
      state.messages.push(message);
    },
    UPDATE_UNREAD_COUNT(state, { chatId, count }) {
      const chat = state.chatList.find(c => c.id === chatId);
      if (chat) {
        chat.unreadCount = count;
      }
    }
  },
  actions: {
    // 获取聊天列表
    getChatList({ commit, rootState }) {
      return new Promise((resolve) => {
        // 这里应该是实际的获取聊天列表API调用
        setTimeout(() => {
          const businessPersonnel = rootState.user.businessPersonnel;
          if (!businessPersonnel) {
            commit('SET_CHAT_LIST', []);
            resolve([]);
            return;
          }
          
          // 模拟聊天列表数据
          const chatList = [
            {
              id: '1',
              type: 'business',
              name: businessPersonnel.name,
              lastMessage: '您好，有什么可以帮助您的吗？',
              lastMessageTime: '10:30',
              unreadCount: 2
            }
          ];
          
          commit('SET_CHAT_LIST', chatList);
          resolve(chatList);
        }, 500);
      });
    },
    
    // 获取聊天消息
    getMessages({ commit }, { chatId, page = 1 }) {
      return new Promise((resolve) => {
        // 这里应该是实际的获取聊天消息API调用
        setTimeout(() => {
          // 模拟聊天消息数据
          const messages = [
            {
              id: '1',
              chatId: chatId,
              sender: 'other',
              content: '您好，有什么可以帮助您的吗？',
              time: '10:30',
              isRead: true
            },
            {
              id: '2',
              chatId: chatId,
              sender: 'self',
              content: '我想咨询一下产品的使用方法',
              time: '10:31',
              isRead: true
            },
            {
              id: '3',
              chatId: chatId,
              sender: 'other',
              content: '好的，请问您具体想了解哪方面的使用方法呢？',
              time: '10:32',
              isRead: false
            }
          ];
          
          if (page === 1) {
            commit('SET_MESSAGES', messages);
          } else {
            // 加载更多消息时，将新消息添加到前面
            commit('SET_MESSAGES', [...messages, ...state.messages]);
          }
          
          // 标记消息为已读
          commit('UPDATE_UNREAD_COUNT', { chatId, count: 0 });
          
          resolve({
            messages,
            hasMore: page < 3 // 模拟只有3页数据
          });
        }, 500);
      });
    },
    
    // 发送消息
    sendMessage({ commit, state }, { chatId, content }) {
      return new Promise((resolve) => {
        // 创建新消息对象
        const newMessage = {
          id: Date.now().toString(),
          chatId: chatId,
          sender: 'self',
          content: content,
          time: formatTime(new Date()),
          isRead: true
        };
        
        // 添加到消息列表
        commit('ADD_MESSAGE', newMessage);
        
        // 这里应该是实际的发送消息API调用
        setTimeout(() => {
          // 模拟业务人员回复
          const reply = {
            id: (Date.now() + 1).toString(),
            chatId: chatId,
            sender: 'other',
            content: '好的，我明白了，稍后会给您详细解答。',
            time: formatTime(new Date(Date.now() + 1000)),
            isRead: true
          };
          
          commit('ADD_MESSAGE', reply);
          
          // 更新聊天列表中的最后一条消息
          const chatList = state.chatList.map(chat => {
            if (chat.id === chatId) {
              return {
                ...chat,
                lastMessage: reply.content,
                lastMessageTime: reply.time
              };
            }
            return chat;
          });
          
          commit('SET_CHAT_LIST', chatList);
          
          resolve(newMessage);
        }, 1000);
      });
    }
  },
  getters: {
    totalUnreadCount: state => {
      return state.chatList.reduce((total, chat) => total + (chat.unreadCount || 0), 0);
    }
  }
};

// 格式化时间的辅助函数
function formatTime(date) {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}
