// 请求拦截器
uni.addInterceptor('request', {
  invoke(args) {
    // 在请求开始前添加自定义逻辑
    console.log('请求拦截器:', args);
    
    // 添加通用请求头
    args.header = {
      ...args.header,
      'Content-Type': 'application/json'
    };
    
    return args;
  },
  success(res) {
    // 请求成功后的回调
    console.log('请求成功:', res);
    return res;
  },
  fail(err) {
    // 请求失败后的回调
    console.error('请求失败:', err);
    
    // 处理CORS错误
    if (err.errMsg && err.errMsg.includes('cors')) {
      console.error('CORS错误，尝试使用代理或后端配置CORS');
      uni.showToast({
        title: '网络请求失败，请检查网络设置',
        icon: 'none',
        duration: 2000
      });
    }
    
    return err;
  },
  complete(res) {
    // 请求完成后的回调（无论成功或失败）
    console.log('请求完成');
    return res;
  }
});

export default {
  // 可以在这里添加其他请求相关的工具方法
};
