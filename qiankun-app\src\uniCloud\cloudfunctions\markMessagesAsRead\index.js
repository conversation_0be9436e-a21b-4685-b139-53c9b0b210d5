'use strict';

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const messagesCollection = db.collection('messages');
  
  // 获取请求参数
  const { userId, businessPersonnelId, messageIds } = event;
  
  // 参数验证
  if (!userId || !businessPersonnelId) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    let updateCondition = {
      userId: userId,
      businessPersonnelId: businessPersonnelId,
      isRead: false
    };
    
    // 如果提供了特定的消息ID列表，则只标记这些消息为已读
    if (messageIds && messageIds.length > 0) {
      updateCondition._id = db.command.in(messageIds);
    }
    
    // 标记消息为已读
    const result = await messagesCollection
      .where(updateCondition)
      .update({
        isRead: true
      });
    
    return {
      code: 200,
      message: '标记消息为已读成功',
      data: {
        updatedCount: result.updated
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};
