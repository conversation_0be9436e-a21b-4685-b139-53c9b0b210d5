'use strict';

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const usersCollection = db.collection('users');
  const businessPersonnelCollection = db.collection('business_personnel');
  
  // 获取请求参数
  const { userId, businessPersonnelId } = event;
  
  // 参数验证
  if (!userId || !businessPersonnelId) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 检查业务人员是否存在
    const businessPersonnelResult = await businessPersonnelCollection.doc(businessPersonnelId).get();
    
    if (businessPersonnelResult.data.length === 0) {
      return {
        code: 404,
        message: '业务人员不存在'
      };
    }
    
    const businessPersonnel = businessPersonnelResult.data[0];
    
    // 更新用户绑定的业务人员
    await usersCollection.doc(userId).update({
      businessPersonnelId: businessPersonnelId,
      updatedAt: new Date()
    });
    
    return {
      code: 200,
      message: '绑定业务人员成功',
      data: {
        businessPersonnel: {
          id: businessPersonnel._id,
          name: businessPersonnel.name,
          department: businessPersonnel.department,
          phone: businessPersonnel.phone
        }
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};
