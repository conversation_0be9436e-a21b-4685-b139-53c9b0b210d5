'use strict';

const crypto = require('crypto');

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const usersCollection = db.collection('users');
  
  // 获取请求参数
  const { phone, password, verificationCode, realName, idCard } = event;
  
  // 参数验证
  if (!phone || !password || !verificationCode || !realName || !idCard) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  // 验证手机号格式
  if (!/^1\d{10}$/.test(phone)) {
    return {
      code: 400,
      message: '手机号格式不正确'
    };
  }
  
  // 验证身份证号格式
  if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)) {
    return {
      code: 400,
      message: '身份证号格式不正确'
    };
  }
  
  // 验证验证码
  // 这里应该有验证验证码的逻辑，但由于是模拟，我们跳过这一步
  
  try {
    // 检查手机号是否已注册
    const existingUser = await usersCollection.where({
      phone: phone
    }).get();
    
    if (existingUser.data.length > 0) {
      return {
        code: 400,
        message: '该手机号已注册'
      };
    }
    
    // 对密码进行加密
    const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');
    
    // 创建新用户
    const result = await usersCollection.add({
      phone,
      password: hashedPassword,
      realName,
      idCard,
      isRealNameVerified: true, // 默认实名认证通过
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // 生成token
    const token = crypto.createHash('md5').update(result.id + Date.now().toString()).digest('hex');
    
    return {
      code: 200,
      message: '注册成功',
      data: {
        token,
        userInfo: {
          id: result.id,
          phone,
          name: realName,
          isRealNameVerified: true
        }
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};
