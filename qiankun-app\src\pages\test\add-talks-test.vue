<template>
  <view class="page-container">
    <view class="card">
      <view class="title">新增对话测试</view>
      
      <view class="info-section">
        <text class="section-title">当前用户信息</text>
        <view class="info-item">
          <text class="info-label">用户ID:</text>
          <text class="info-value">{{ currentUser.id || '未登录' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用户名:</text>
          <text class="info-value">{{ currentUser.name || '未知' }}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="form-section">
        <text class="section-title">测试对话内容</text>
        
        <view class="form-group">
          <text class="form-label">用户问题:</text>
          <textarea 
            class="form-textarea" 
            v-model="testData.question" 
            placeholder="请输入用户问题..."
            maxlength="500"
          ></textarea>
        </view>
        
        <view class="form-group">
          <text class="form-label">AI回答:</text>
          <textarea 
            class="form-textarea" 
            v-model="testData.answer" 
            placeholder="请输入AI回答..."
            maxlength="1000"
          ></textarea>
        </view>
        
        <button class="test-btn" @click="testAddTalks" :disabled="isLoading">
          <text v-if="!isLoading">测试新增对话</text>
          <text v-else>保存中...</text>
        </button>
        
        <button class="test-btn secondary" @click="fillSampleData">
          <text>填充示例数据</text>
        </button>
        
        <button class="test-btn secondary" @click="clearData">
          <text>清空数据</text>
        </button>
      </view>
      
      <view class="divider"></view>
      
      <view class="result-section">
        <text class="section-title">API响应结果</text>
        <view class="result-container">
          <text class="result-text">{{ responseText }}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="log-section">
        <text class="section-title">操作日志</text>
        <view class="log-container">
          <text 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
          >
            {{ log }}
          </text>
        </view>
        
        <button class="clear-log-btn" @click="clearLogs" v-if="logs.length > 0">
          <text>清空日志</text>
        </button>
      </view>
      
      <view class="divider"></view>
      
      <view class="action-section">
        <text class="section-title">快速操作</text>
        
        <button class="action-btn" @click="goToAiChat">
          <text>跳转到AI对话页面</text>
        </button>
        
        <button class="action-btn" @click="goToHistory">
          <text>查看历史记录</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'
import { getUserInfo } from '@/utils/auth.js'

export default {
  data() {
    return {
      currentUser: {},
      isLoading: false,
      responseText: '暂无响应',
      testData: {
        question: '',
        answer: ''
      },
      logs: []
    }
  },
  onLoad() {
    this.currentUser = getUserInfo() || {};
    this.addLog('页面加载完成');
  },
  methods: {
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      
      // 限制日志数量
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    },
    
    // 测试新增对话
    async testAddTalks() {
      if (!this.currentUser.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }
      
      if (!this.testData.question.trim()) {
        uni.showToast({
          title: '请输入用户问题',
          icon: 'none'
        });
        return;
      }
      
      if (!this.testData.answer.trim()) {
        uni.showToast({
          title: '请输入AI回答',
          icon: 'none'
        });
        return;
      }
      
      this.isLoading = true;
      this.responseText = '请求中...';
      this.addLog('开始测试AddTalks接口');
      
      try {
        console.log('测试数据:', {
          question: this.testData.question,
          answer: this.testData.answer,
          user_id: this.currentUser.id
        });
        
        const response = await userApi.addTalks(
          this.testData.question,
          this.testData.answer,
          this.currentUser.id
        );
        
        console.log('AddTalks响应:', response);
        
        this.responseText = JSON.stringify(response, null, 2);
        
        if (response && response.code === 200) {
          this.addLog('对话保存成功');
          
          uni.showToast({
            title: '对话保存成功',
            icon: 'success'
          });
          
          // 清空表单
          this.testData.question = '';
          this.testData.answer = '';
        } else {
          throw new Error(`API返回错误: ${response.code}`);
        }
      } catch (error) {
        console.error('测试AddTalks失败:', error);
        
        this.responseText = `错误: ${error.message}`;
        this.addLog(`保存失败: ${error.message}`);
        
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 填充示例数据
    fillSampleData() {
      this.testData.question = '你好，请问如何使用这个AI助手？';
      this.testData.answer = '您好！欢迎使用我们的AI助手。您可以通过以下方式与我互动：\n\n1. 直接输入您的问题\n2. 我会根据您的问题提供相应的回答\n3. 您可以继续追问或提出新的问题\n\n我可以帮助您解答各种问题，包括技术咨询、产品使用指导等。有什么我可以帮助您的吗？';
      
      this.addLog('已填充示例数据');
      
      uni.showToast({
        title: '示例数据已填充',
        icon: 'success'
      });
    },
    
    // 清空数据
    clearData() {
      this.testData.question = '';
      this.testData.answer = '';
      this.responseText = '暂无响应';
      
      this.addLog('已清空测试数据');
      
      uni.showToast({
        title: '数据已清空',
        icon: 'success'
      });
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      
      uni.showToast({
        title: '日志已清空',
        icon: 'success'
      });
    },
    
    // 跳转到AI对话页面
    goToAiChat() {
      uni.switchTab({
        url: '/pages/chat/ai-chat'
      });
    },
    
    // 查看历史记录
    goToHistory() {
      uni.switchTab({
        url: '/pages/chat/chat-history'
      });
    }
  }
}
</script>

<style>
.info-section, .form-section, .result-section, .log-section, .action-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.form-textarea {
  width: 100%;
  min-height: 150rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #007AFF;
  outline: none;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn:active {
  background: #0056CC;
}

.test-btn:disabled {
  background: #ccc;
}

.test-btn.secondary {
  background: #6c757d;
}

.test-btn.secondary:active {
  background: #545b62;
}

.result-container, .log-container {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-text {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  display: block;
  line-height: 1.5;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.log-item:last-child {
  margin-bottom: 0;
}

.clear-log-btn {
  width: 100%;
  height: 60rpx;
  background: #dc3545;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-top: 20rpx;
}

.clear-log-btn:active {
  background: #c82333;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  background: #28a745;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.action-btn:active {
  background: #218838;
}
</style>
