<template>
  <view class="content">
    <image class="logo" src="/static/images/logo.png"></image>
    <view class="welcome-text">
      <text class="title">欢迎使用乾坤袋</text>
      <text class="subtitle">实名制用户与业务人员的实时沟通平台</text>
    </view>

    <view class="action-buttons">
      <button class="btn primary" @click="navigateToLogin">登录</button>
      <button class="btn secondary" @click="navigateToRegister">注册</button>
    </view>

    <view class="qr-scan-section">
      <text class="qr-text">需要业务人员二维码才能使用</text>
      <button class="btn scan" @click="navigateToScanQR">
        <text class="btn-text">扫描二维码</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '乾坤袋',
    }
  },
  onLoad() {
    // 检查用户是否已登录
    const token = uni.getStorageSync('token');
    if (token) {
      // 已登录，检查是否已绑定业务人员
      const businessPersonnel = uni.getStorageSync('businessPersonnel');
      if (businessPersonnel) {
        // 已绑定业务人员，跳转到消息列表页
        uni.switchTab({
          url: '/pages/chat/chat-list'
        });
      } else {
        // 未绑定业务人员，跳转到扫描二维码页
        uni.navigateTo({
          url: '/pages/auth/scan-qr'
        });
      }
    }
  },
  methods: {
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/auth/login'
      });
    },
    navigateToRegister() {
      uni.navigateTo({
        url: '/pages/auth/register'
      });
    },
    navigateToScanQR() {
      uni.navigateTo({
        url: '/pages/auth/scan-qr'
      });
    }
  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  height: 100vh;
  background-color: #f8f8f8;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 100rpx;
  margin-bottom: 60rpx;
}

.welcome-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.action-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 60rpx;
}

.btn {
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.primary {
  background-color: #007AFF;
  color: #fff;
}

.secondary {
  background-color: #fff;
  color: #007AFF;
  border: 1px solid #007AFF;
}

.qr-scan-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
}

.qr-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.scan {
  background-color: #4CD964;
  color: #fff;
  width: 400rpx;
}

.btn-text {
  color: #fff;
}
</style>
