/**
 * 对话记录管理工具类
 * 统一处理对话记录的保存、获取等操作
 */

import { userApi } from './api.js';

/**
 * 保存对话记录到后端
 * @param {string} question - 用户问题
 * @param {string} answer - AI回答
 * @param {string} userId - 用户ID（可选，不传则从缓存获取）
 * @param {boolean} silent - 是否静默保存（不输出日志）
 * @returns {Promise<boolean>} 保存是否成功
 */
export const saveTalkRecord = async (question, answer, userId = null, silent = false) => {
  try {
    // 获取用户ID
    let currentUserId = userId;
    if (!currentUserId) {
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        if (!silent) console.warn('用户未登录，无法保存对话记录');
        return false;
      }
      currentUserId = userInfo.id;
    }

    // 数据验证
    if (!question || !answer) {
      if (!silent) console.warn('问题或答案为空，跳过保存');
      return false;
    }

    // 数据清理和截断
    const cleanQuestion = question.trim();
    const cleanAnswer = answer.trim();
    
    // 限制长度（防止数据库字段溢出）
    const maxLength = 5000;
    const finalQuestion = cleanQuestion.length > maxLength 
      ? cleanQuestion.substring(0, maxLength) + '...[内容过长已截断]'
      : cleanQuestion;
    const finalAnswer = cleanAnswer.length > maxLength 
      ? cleanAnswer.substring(0, maxLength) + '...[内容过长已截断]'
      : cleanAnswer;

    if (!silent) {
      console.log('💾 保存对话记录:', {
        question: finalQuestion.substring(0, 100) + (finalQuestion.length > 100 ? '...' : ''),
        answer: finalAnswer.substring(0, 100) + (finalAnswer.length > 100 ? '...' : ''),
        user_id: currentUserId,
        timestamp: new Date().toISOString()
      });
    }

    // 调用AddTalks接口
    const response = await userApi.addTalks(finalQuestion, finalAnswer, currentUserId);

    if (response && response.code === 200) {
      if (!silent) console.log('✅ 对话记录保存成功');
      return true;
    } else {
      if (!silent) {
        switch (response?.code) {
          case 401:
            console.warn('❌ 对话记录保存失败: 用户未授权');
            break;
          case 500:
            console.warn('❌ 对话记录保存失败: 服务器内部错误');
            break;
          default:
            console.warn('❌ 对话记录保存失败:', response);
        }
      }
      return false;
    }
  } catch (error) {
    if (!silent) {
      console.error('💥 保存对话记录异常:', error);
      
      // 记录详细的错误信息用于调试
      if (error.message) {
        console.error('错误信息:', error.message);
      }
    }
    return false;
  }
};

/**
 * 批量保存对话记录
 * @param {Array} talks - 对话记录数组 [{question, answer}, ...]
 * @param {string} userId - 用户ID（可选）
 * @returns {Promise<{success: number, failed: number}>} 保存结果统计
 */
export const saveTalkRecords = async (talks, userId = null) => {
  const results = {
    success: 0,
    failed: 0
  };

  if (!Array.isArray(talks) || talks.length === 0) {
    console.warn('对话记录数组为空');
    return results;
  }

  console.log(`📦 开始批量保存 ${talks.length} 条对话记录`);

  for (let i = 0; i < talks.length; i++) {
    const talk = talks[i];
    if (talk.question && talk.answer) {
      const success = await saveTalkRecord(talk.question, talk.answer, userId, true);
      if (success) {
        results.success++;
      } else {
        results.failed++;
      }
    } else {
      results.failed++;
    }

    // 添加延迟避免请求过于频繁
    if (i < talks.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`📊 批量保存完成: 成功 ${results.success} 条，失败 ${results.failed} 条`);
  return results;
};

/**
 * 获取对话记录
 * @param {string} userId - 用户ID（可选）
 * @param {number} flag - 标志位（1=获取自己的历史记录）
 * @returns {Promise<Array>} 对话记录数组
 */
export const getTalkRecords = async (userId = null, flag = null) => {
  try {
    // 获取用户ID
    let currentUserId = userId;
    if (!currentUserId) {
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        console.warn('用户未登录，无法获取对话记录');
        return [];
      }
      currentUserId = userInfo.id;
    }

    console.log('📖 获取对话记录，用户ID:', currentUserId, 'flag:', flag);

    // 调用GetTalks接口
    const response = await userApi.getTalks(currentUserId, flag);

    if (response && response.code === 200 && response.data) {
      console.log(`✅ 获取对话记录成功，共 ${response.data.length} 条`);
      return response.data;
    } else if (response && response.code === 203) {
      console.warn('❌ 超级管理员无法查看对话记录');
      return [];
    } else if (response && response.code === 204) {
      console.log('📭 暂无对话记录');
      return [];
    } else {
      console.warn('❌ 获取对话记录失败:', response);
      return [];
    }
  } catch (error) {
    console.error('💥 获取对话记录异常:', error);
    return [];
  }
};

/**
 * 获取自己的历史对话记录
 * @param {string} userId - 用户ID（可选）
 * @returns {Promise<Array>} 历史对话记录数组
 */
export const getMyTalkHistory = async (userId = null) => {
  return await getTalkRecords(userId, 1);
};

/**
 * 清理本地对话缓存
 */
export const clearLocalTalkCache = () => {
  try {
    const cacheKeys = [
      'chatHistory',
      'conversations',
      'messageHistory',
      'localTalks'
    ];

    cacheKeys.forEach(key => {
      try {
        uni.removeStorageSync(key);
        console.log(`🗑️ 已清空对话缓存: ${key}`);
      } catch (error) {
        console.warn(`清空缓存失败: ${key}`, error);
      }
    });

    console.log('✅ 本地对话缓存清理完成');
    return true;
  } catch (error) {
    console.error('💥 清理本地对话缓存失败:', error);
    return false;
  }
};

/**
 * 格式化对话记录用于显示
 * @param {Object} talk - 对话记录对象
 * @returns {Object} 格式化后的对话记录
 */
export const formatTalkRecord = (talk) => {
  if (!talk) return null;

  return {
    id: talk.talk_id || talk.id,
    userId: talk.id || talk.user_id,
    userName: talk.name || '未知用户',
    userLevel: talk.level || 3,
    userPicture: talk.picture || '',
    question: talk.question || '',
    answer: talk.answer || '',
    date: talk.date || new Date().toISOString(),
    formattedDate: formatTalkDate(talk.date)
  };
};

/**
 * 格式化对话时间
 * @param {string} dateString - 时间字符串
 * @returns {string} 格式化后的时间
 */
export const formatTalkDate = (dateString) => {
  if (!dateString) return '未知时间';

  try {
    const date = new Date(dateString);
    const now = new Date();

    // 如果是今天的消息，只显示时间
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }

    // 如果是昨天的消息，显示"昨天"
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }

    // 其他情况显示完整日期
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  } catch (error) {
    console.error('时间格式化失败:', error);
    return '时间格式错误';
  }
};

/**
 * 验证对话记录数据
 * @param {Object} talk - 对话记录对象
 * @returns {boolean} 是否有效
 */
export const validateTalkRecord = (talk) => {
  if (!talk || typeof talk !== 'object') {
    return false;
  }

  // 必须有问题和答案
  if (!talk.question || !talk.answer) {
    return false;
  }

  // 问题和答案不能为空字符串
  if (talk.question.trim() === '' || talk.answer.trim() === '') {
    return false;
  }

  return true;
};
