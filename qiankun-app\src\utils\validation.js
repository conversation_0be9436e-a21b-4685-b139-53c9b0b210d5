/**
 * 表单验证工具类
 */

// 验证手机号
export const validatePhone = (phone) => {
  if (!phone) {
    return {
      valid: false,
      message: '请输入手机号码'
    };
  }
  
  if (!/^1\d{10}$/.test(phone)) {
    return {
      valid: false,
      message: '手机号码格式不正确'
    };
  }
  
  return {
    valid: true
  };
};

// 验证密码
export const validatePassword = (password) => {
  if (!password) {
    return {
      valid: false,
      message: '请输入密码'
    };
  }
  
  if (password.length < 6 || password.length > 20) {
    return {
      valid: false,
      message: '密码长度应为6-20位'
    };
  }
  
  return {
    valid: true
  };
};

// 验证两次密码是否一致
export const validatePasswordConfirm = (password, confirmPassword) => {
  if (!confirmPassword) {
    return {
      valid: false,
      message: '请再次输入密码'
    };
  }
  
  if (password !== confirmPassword) {
    return {
      valid: false,
      message: '两次输入的密码不一致'
    };
  }
  
  return {
    valid: true
  };
};

// 验证验证码
export const validateVerificationCode = (code) => {
  if (!code) {
    return {
      valid: false,
      message: '请输入验证码'
    };
  }
  
  if (!/^\d{6}$/.test(code)) {
    return {
      valid: false,
      message: '验证码格式不正确'
    };
  }
  
  return {
    valid: true
  };
};

// 验证真实姓名
export const validateRealName = (realName) => {
  if (!realName) {
    return {
      valid: false,
      message: '请输入真实姓名'
    };
  }
  
  if (realName.length < 2) {
    return {
      valid: false,
      message: '真实姓名长度不正确'
    };
  }
  
  return {
    valid: true
  };
};

// 验证身份证号
export const validateIdCard = (idCard) => {
  if (!idCard) {
    return {
      valid: false,
      message: '请输入身份证号码'
    };
  }
  
  if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)) {
    return {
      valid: false,
      message: '身份证号码格式不正确'
    };
  }
  
  return {
    valid: true
  };
};
