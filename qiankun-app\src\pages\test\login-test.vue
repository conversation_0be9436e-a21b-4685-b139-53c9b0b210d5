<template>
  <view class="page-container">
    <view class="card">
      <view class="title">登录API测试</view>
      
      <view class="form-group">
        <view class="form-label">用户名</view>
        <input class="form-input" v-model="testForm.username" placeholder="请输入用户名" />
      </view>
      
      <view class="form-group">
        <view class="form-label">密码</view>
        <input class="form-input" type="password" v-model="testForm.password" placeholder="请输入密码" />
      </view>
      
      <button class="btn btn-primary" @click="testLogin" :disabled="isLoading">
        {{ isLoading ? '测试中...' : '测试登录API' }}
      </button>
      
      <view class="divider"></view>
      
      <view class="title">API响应结果</view>
      <view class="response-container">
        <text class="response-text">{{ responseText }}</text>
      </view>
      
      <view class="divider"></view>
      
      <view class="title">错误信息</view>
      <view class="error-container">
        <text class="error-text">{{ errorText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'

export default {
  data() {
    return {
      testForm: {
        username: '',
        password: ''
      },
      isLoading: false,
      responseText: '暂无响应',
      errorText: '暂无错误'
    }
  },
  methods: {
    async testLogin() {
      if (!this.testForm.username || !this.testForm.password) {
        uni.showToast({
          title: '请输入用户名和密码',
          icon: 'none'
        });
        return;
      }
      
      this.isLoading = true;
      this.responseText = '请求中...';
      this.errorText = '暂无错误';
      
      try {
        const response = await userApi.login(this.testForm.username, this.testForm.password);
        this.responseText = JSON.stringify(response, null, 2);
        this.errorText = '暂无错误';
        
        uni.showToast({
          title: '请求成功',
          icon: 'success'
        });
      } catch (error) {
        this.errorText = error.message || error.toString();
        this.responseText = '请求失败';
        
        uni.showToast({
          title: '请求失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
}
</script>

<style>
.response-container, .error-container {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  min-height: 200rpx;
}

.response-text, .error-text {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
}

.error-text {
  color: #ff3b30;
}
</style>
