'use strict';

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const messagesCollection = db.collection('messages');
  const businessPersonnelCollection = db.collection('business_personnel');
  
  // 获取请求参数
  const { userId } = event;
  
  // 参数验证
  if (!userId) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 获取用户与所有业务人员的最新消息
    const $ = db.command.aggregate;
    const result = await messagesCollection.aggregate()
      .match({
        userId: userId
      })
      .sort({
        createdAt: -1
      })
      .group({
        _id: '$businessPersonnelId',
        lastMessage: $.first('$content'),
        lastMessageTime: $.first('$createdAt'),
        lastMessageId: $.first('$_id')
      })
      .end();
    
    if (result.data.length === 0) {
      return {
        code: 200,
        message: '获取聊天列表成功',
        data: {
          chatList: []
        }
      };
    }
    
    // 获取业务人员信息
    const businessPersonnelIds = result.data.map(item => item._id);
    const businessPersonnelResult = await businessPersonnelCollection
      .where({
        _id: db.command.in(businessPersonnelIds)
      })
      .get();
    
    const businessPersonnelMap = {};
    businessPersonnelResult.data.forEach(bp => {
      businessPersonnelMap[bp._id] = bp;
    });
    
    // 获取未读消息数量
    const unreadCountPromises = businessPersonnelIds.map(bpId => {
      return messagesCollection
        .where({
          userId: userId,
          businessPersonnelId: bpId,
          sender: 'business', // 只计算业务人员发送的未读消息
          isRead: false
        })
        .count();
    });
    
    const unreadCountResults = await Promise.all(unreadCountPromises);
    
    // 组装聊天列表数据
    const chatList = result.data.map((item, index) => {
      const bp = businessPersonnelMap[item._id] || {};
      return {
        id: item._id,
        type: 'business',
        name: bp.name || '未知业务人员',
        department: bp.department,
        lastMessage: item.lastMessage,
        lastMessageTime: formatTime(item.lastMessageTime),
        unreadCount: unreadCountResults[index] ? unreadCountResults[index].total : 0
      };
    });
    
    return {
      code: 200,
      message: '获取聊天列表成功',
      data: {
        chatList
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};

// 格式化时间的辅助函数
function formatTime(date) {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}
