<template>
  <view class="password-page">
    <!-- 顶部装饰 -->
    <view class="header-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 主要内容 -->
    <view class="content-container">
      <!-- 标题区域 -->
      <view class="header-section">
        <view class="icon-container">
          <text class="security-icon">🔐</text>
        </view>
        <text class="main-title">修改密码</text>
        <text class="sub-title">为了您的账户安全，请定期修改密码</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon">🔒</text>
            </view>
            <input
              class="password-input"
              type="password"
              placeholder="请输入原密码"
              v-model="form.oldPassword"
              password
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon">🆕</text>
            </view>
            <input
              class="password-input"
              type="password"
              placeholder="请输入新密码"
              v-model="form.newPassword"
              password
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon">✅</text>
            </view>
            <input
              class="password-input"
              type="password"
              placeholder="请再次输入新密码"
              v-model="form.confirmPassword"
              password
            />
          </view>
        </view>

        <!-- 密码要求提示 -->
        <view class="tips-card">
          <view class="tips-header">
            <text class="tips-icon">💡</text>
            <text class="tips-title">密码安全建议</text>
          </view>
          <view class="tips-content">
            <view class="tip-item">
              <text class="tip-dot">•</text>
              <text class="tip-text">长度至少6位字符</text>
            </view>
            <view class="tip-item">
              <text class="tip-dot">•</text>
              <text class="tip-text">建议包含字母和数字</text>
            </view>
            <view class="tip-item">
              <text class="tip-dot">•</text>
              <text class="tip-text">避免使用生日等简单密码</text>
            </view>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="button-section">
          <button class="primary-button" @click="handleChangePassword" :disabled="isLoading">
            <view class="button-content">
              <text v-if="!isLoading" class="button-text">确认修改</text>
              <view v-else class="loading-content">
                <view class="loading-spinner"></view>
                <text class="button-text">修改中...</text>
              </view>
            </view>
          </button>

          <button class="secondary-button" @click="goBack" :disabled="isLoading">
            <text class="button-text">取消</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'
import { getUserInfo } from '@/utils/auth.js'

export default {
  data() {
    return {
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      isLoading: false
    }
  },
  methods: {
    // 验证表单
    validateForm() {
      if (!this.form.oldPassword) {
        uni.showToast({
          title: '请输入原密码',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.form.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.newPassword.length < 6) {
        uni.showToast({
          title: '新密码长度至少6位',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.form.confirmPassword) {
        uni.showToast({
          title: '请确认新密码',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.newPassword !== this.form.confirmPassword) {
        uni.showToast({
          title: '两次输入的新密码不一致',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.oldPassword === this.form.newPassword) {
        uni.showToast({
          title: '新密码不能与原密码相同',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 处理修改密码
    async handleChangePassword() {
      if (!this.validateForm()) {
        return;
      }

      // 获取用户信息
      const userInfo = getUserInfo();
      if (!userInfo || !userInfo.id) {
        uni.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none'
        });
        return;
      }

      this.isLoading = true;

      try {
        console.log('开始修改密码，用户ID:', userInfo.id);

        // 先验证原密码是否正确（通过登录接口验证）
        try {
          await userApi.login(userInfo.username, this.form.oldPassword);
          console.log('原密码验证成功');
        } catch (loginError) {
          console.error('原密码验证失败:', loginError);
          uni.showToast({
            title: '原密码错误',
            icon: 'none'
          });
          this.isLoading = false;
          return;
        }

        // 调用修改密码API
        const response = await userApi.changePassword(
          userInfo.id,
          this.form.oldPassword,
          this.form.newPassword
        );

        console.log('修改密码响应:', response);

        if (response && response.code === 200) {
          // 修改成功
          uni.showToast({
            title: '密码修改成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                this.goBack();
              }, 2000);
            }
          });
        } else {
          // 修改失败
          let errorMessage = '修改密码失败';
          if (response && response.code) {
            switch (response.code) {
              case 400:
                errorMessage = '参数错误';
                break;
              case 401:
                errorMessage = '原密码错误';
                break;
              case 204:
                errorMessage = '用户不存在';
                break;
              case 500:
                errorMessage = '服务器内部错误';
                break;
              default:
                errorMessage = response.message || '修改密码失败';
            }
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('修改密码错误:', error);

        let errorMessage = '网络错误，请稍后重试';
        if (error.message) {
          if (error.message.includes('未登录')) {
            errorMessage = '登录已过期，请重新登录';
          } else {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style>
.password-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 50rpx;
  left: -75rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 150rpx;
  right: 100rpx;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 主要内容 */
.content-container {
  position: relative;
  z-index: 10;
  padding: 100rpx 40rpx 40rpx;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.icon-container {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx;
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.security-icon {
  font-size: 60rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}

.sub-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 表单区域 */
.form-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  backdrop-filter: blur(20px);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 输入框组 */
.input-group {
  margin-bottom: 40rpx;
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-icon {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.icon {
  font-size: 32rpx;
}

.password-input {
  width: 100%;
  height: 100rpx;
  padding: 0 30rpx 0 80rpx;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.password-input::placeholder {
  color: #999;
}

/* 提示卡片 */
.tips-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 40rpx 0;
  color: #fff;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
}

.tips-content {
  padding-left: 47rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.tip-dot {
  font-size: 24rpx;
  margin-right: 15rpx;
  margin-top: 4rpx;
}

.tip-text {
  font-size: 26rpx;
  line-height: 1.5;
  opacity: 0.9;
}

/* 按钮区域 */
.button-section {
  margin-top: 60rpx;
}

.primary-button {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  border: none;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.primary-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 5rpx 15rpx rgba(102, 126, 234, 0.4);
}

.primary-button:disabled {
  opacity: 0.7;
  transform: none;
}

.secondary-button {
  width: 100%;
  height: 100rpx;
  background: transparent;
  border: 2rpx solid #ddd;
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.secondary-button:active {
  background: #f5f5f5;
  border-color: #ccc;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.button-text {
  font-size: 32rpx;
  font-weight: bold;
}

.primary-button .button-text {
  color: #fff;
}

.secondary-button .button-text {
  color: #666;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  margin-right: 15rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
