{"bsonType": "object", "required": ["userId", "businessPersonnelId", "content"], "permission": {"read": "doc.userId == auth.uid || doc.businessPersonnelId == auth.uid", "create": true, "update": "doc.userId == auth.uid || doc.businessPersonnelId == auth.uid", "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "userId": {"bsonType": "string", "description": "用户ID", "foreignKey": "users._id"}, "businessPersonnelId": {"bsonType": "string", "description": "业务人员ID", "foreignKey": "business_personnel._id"}, "sender": {"bsonType": "string", "description": "发送者类型，user或business", "enum": ["user", "business"]}, "content": {"bsonType": "string", "description": "消息内容"}, "isRead": {"bsonType": "bool", "description": "是否已读", "defaultValue": false}, "createdAt": {"bsonType": "timestamp", "description": "创建时间", "defaultValue": {"$env": "now"}}}}