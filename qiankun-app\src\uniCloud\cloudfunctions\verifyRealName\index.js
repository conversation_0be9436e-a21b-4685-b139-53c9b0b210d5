'use strict';

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const usersCollection = db.collection('users');
  
  // 获取请求参数
  const { userId, realName, idCard } = event;
  
  // 参数验证
  if (!userId || !realName || !idCard) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  // 验证身份证号格式
  if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)) {
    return {
      code: 400,
      message: '身份证号格式不正确'
    };
  }
  
  try {
    // 这里应该有调用实名认证服务的逻辑
    // 由于是模拟，我们假设认证总是成功的
    
    // 更新用户实名认证信息
    await usersCollection.doc(userId).update({
      realName: realName,
      idCard: idCard,
      isRealNameVerified: true,
      updatedAt: new Date()
    });
    
    return {
      code: 200,
      message: '实名认证成功',
      data: {
        isRealNameVerified: true
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};
