<template>
  <view class="page-container">
    <view class="card">
      <view class="title">AI对话历史测试</view>
      
      <view class="info-section">
        <text class="section-title">当前用户信息</text>
        <view class="info-item">
          <text class="info-label">用户ID:</text>
          <text class="info-value">{{ currentUser.id || '未登录' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用户名:</text>
          <text class="info-value">{{ currentUser.name || '未知' }}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="test-section">
        <text class="section-title">测试功能</text>
        
        <button class="test-btn" @click="testGetMyHistory" :disabled="isLoading">
          <text v-if="!isLoading">获取我的历史对话</text>
          <text v-else>获取中...</text>
        </button>
        
        <button class="test-btn" @click="testGetTalksWithFlag" :disabled="isLoading">
          <text v-if="!isLoading">测试GetTalks(flag=1)</text>
          <text v-else>测试中...</text>
        </button>
        
        <button class="test-btn secondary" @click="goToAiChat">
          <text>跳转到AI对话页面</text>
        </button>
        
        <button class="test-btn secondary" @click="clearTestData">
          <text>清空测试数据</text>
        </button>
      </view>
      
      <view class="divider"></view>
      
      <view class="result-section">
        <text class="section-title">API响应结果</text>
        <view class="result-container">
          <text class="result-text">{{ responseText }}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="history-section" v-if="historyTalks.length > 0">
        <text class="section-title">历史对话 ({{ historyTalks.length }}条)</text>
        <view class="history-list">
          <view 
            class="history-item" 
            v-for="(talk, index) in historyTalks.slice(0, 5)" 
            :key="index"
          >
            <view class="history-header">
              <text class="talk-id">ID: {{ talk.talk_id }}</text>
              <text class="talk-date">{{ formatDate(talk.date) }}</text>
            </view>
            <view class="history-content">
              <view class="question-bubble">
                <text class="bubble-label">用户问题:</text>
                <text class="bubble-text">{{ talk.question }}</text>
              </view>
              <view class="answer-bubble">
                <text class="bubble-label">AI回答:</text>
                <text class="bubble-text">{{ talk.answer }}</text>
              </view>
            </view>
          </view>
          
          <view v-if="historyTalks.length > 5" class="more-history">
            <text class="more-text">还有 {{ historyTalks.length - 5 }} 条历史记录...</text>
          </view>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="log-section">
        <text class="section-title">操作日志</text>
        <view class="log-container">
          <text 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
          >
            {{ log }}
          </text>
        </view>
        
        <button class="clear-log-btn" @click="clearLogs" v-if="logs.length > 0">
          <text>清空日志</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyTalkHistory, getTalkRecords } from '@/utils/talks.js'
import { getUserInfo } from '@/utils/auth.js'

export default {
  data() {
    return {
      currentUser: {},
      isLoading: false,
      responseText: '暂无响应',
      historyTalks: [],
      logs: []
    }
  },
  onLoad() {
    this.currentUser = getUserInfo() || {};
    this.addLog('页面加载完成');
  },
  methods: {
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      
      // 限制日志数量
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    },
    
    // 测试获取我的历史对话
    async testGetMyHistory() {
      if (!this.currentUser.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }
      
      this.isLoading = true;
      this.responseText = '请求中...';
      this.addLog('开始获取我的历史对话');
      
      try {
        const historyData = await getMyTalkHistory();
        
        console.log('我的历史对话:', historyData);
        
        this.responseText = JSON.stringify(historyData, null, 2);
        this.historyTalks = historyData || [];
        
        if (historyData && historyData.length > 0) {
          this.addLog(`获取成功，共${historyData.length}条历史对话`);
          
          uni.showToast({
            title: `获取成功，共${historyData.length}条对话`,
            icon: 'success'
          });
        } else {
          this.addLog('暂无历史对话记录');
          
          uni.showToast({
            title: '暂无历史对话记录',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取我的历史对话失败:', error);
        
        this.responseText = `错误: ${error.message}`;
        this.addLog(`获取失败: ${error.message}`);
        
        uni.showToast({
          title: error.message || '获取失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 测试GetTalks接口带flag参数
    async testGetTalksWithFlag() {
      if (!this.currentUser.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }
      
      this.isLoading = true;
      this.responseText = '请求中...';
      this.addLog('开始测试GetTalks接口(flag=1)');
      
      try {
        const talksData = await getTalkRecords(this.currentUser.id, 1);
        
        console.log('GetTalks(flag=1)响应:', talksData);
        
        this.responseText = JSON.stringify(talksData, null, 2);
        this.historyTalks = talksData || [];
        
        if (talksData && talksData.length > 0) {
          this.addLog(`GetTalks(flag=1)成功，共${talksData.length}条记录`);
          
          uni.showToast({
            title: `获取成功，共${talksData.length}条记录`,
            icon: 'success'
          });
        } else {
          this.addLog('GetTalks(flag=1)返回空数据');
          
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('GetTalks(flag=1)失败:', error);
        
        this.responseText = `错误: ${error.message}`;
        this.addLog(`GetTalks(flag=1)失败: ${error.message}`);
        
        uni.showToast({
          title: error.message || '获取失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 跳转到AI对话页面
    goToAiChat() {
      uni.switchTab({
        url: '/pages/chat/ai-chat'
      });
    },
    
    // 清空测试数据
    clearTestData() {
      this.responseText = '暂无响应';
      this.historyTalks = [];
      
      this.addLog('已清空测试数据');
      
      uni.showToast({
        title: '数据已清空',
        icon: 'success'
      });
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      
      uni.showToast({
        title: '日志已清空',
        icon: 'success'
      });
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知时间';
      
      const date = new Date(dateString);
      const now = new Date();
      
      // 如果是今天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      }
      
      // 如果是昨天的消息，显示"昨天"
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      }
      
      // 其他情况显示完整日期
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  }
}
</script>

<style>
.info-section, .test-section, .result-section, .history-section, .log-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn:active {
  background: #0056CC;
}

.test-btn:disabled {
  background: #ccc;
}

.test-btn.secondary {
  background: #6c757d;
}

.test-btn.secondary:active {
  background: #545b62;
}

.result-container, .log-container {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-text {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
}

.history-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.history-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border: 1rpx solid #e5e5e5;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.talk-id {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

.talk-date {
  font-size: 22rpx;
  color: #999;
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.question-bubble, .answer-bubble {
  padding: 15rpx;
  border-radius: 12rpx;
}

.question-bubble {
  background: #E3F2FD;
  border-left: 4rpx solid #2196F3;
}

.answer-bubble {
  background: #F5F5F5;
  border-left: 4rpx solid #4CAF50;
}

.bubble-label {
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.bubble-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.more-history {
  text-align: center;
  padding: 20rpx;
}

.more-text {
  font-size: 26rpx;
  color: #666;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  display: block;
  line-height: 1.5;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.log-item:last-child {
  margin-bottom: 0;
}

.clear-log-btn {
  width: 100%;
  height: 60rpx;
  background: #dc3545;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-top: 20rpx;
}

.clear-log-btn:active {
  background: #c82333;
}
</style>
