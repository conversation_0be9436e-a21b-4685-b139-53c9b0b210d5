<template>
  <view class="custom-tab-bar">
    <view 
      class="tab-item" 
      v-for="(tab, index) in visibleTabs" 
      :key="index"
      :class="{ 'active': currentTab === tab.path }"
      @click="switchTab(tab.path)"
    >
      <view class="tab-icon">
        <uni-icons 
          :type="tab.icon" 
          :size="24" 
          :color="currentTab === tab.path ? '#007AFF' : '#7A7E83'"
        ></uni-icons>
      </view>
      <text class="tab-text" :class="{ 'active': currentTab === tab.path }">
        {{ tab.text }}
      </text>
    </view>
  </view>
</template>

<script>
import { canAccessUserManagement, canAccessChatHistory } from '@/utils/auth.js'

export default {
  name: 'CustomTabBar',
  props: {
    current: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentTab: '',
      allTabs: [
        {
          path: '/pages/chat/ai-chat',
          icon: 'chat',
          text: 'AI对话'
        },
        {
          path: '/pages/chat/chat-history',
          icon: 'list',
          text: '历史记录',
          requirePermission: 'chatHistory'
        },
        {
          path: '/pages/admin/user-management',
          icon: 'contact',
          text: '用户管理',
          requirePermission: 'userManagement'
        },
        {
          path: '/pages/user/profile',
          icon: 'person',
          text: '我的'
        }
      ]
    }
  },
  computed: {
    visibleTabs() {
      return this.allTabs.filter(tab => {
        // 如果选项卡需要权限检查
        if (tab.requirePermission) {
          switch (tab.requirePermission) {
            case 'userManagement':
              return canAccessUserManagement();
            case 'chatHistory':
              return canAccessChatHistory();
            default:
              return false;
          }
        }
        return true;
      });
    }
  },
  mounted() {
    this.currentTab = this.current;

    // 监听用户信息变化，当用户登录/退出时重新计算可见的tabs
    uni.$on('userInfoChanged', this.updateVisibleTabs);
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('userInfoChanged', this.updateVisibleTabs);
  },
  watch: {
    current(newVal) {
      this.currentTab = newVal;
    }
  },
  methods: {
    switchTab(path) {
      if (this.currentTab === path) return;

      this.currentTab = path;
      uni.switchTab({
        url: path
      });
    },

    // 更新可见的tabs（强制重新计算）
    updateVisibleTabs() {
      this.$forceUpdate();
    }
  }
}
</script>

<style>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item:active {
  background-color: #f5f5f5;
}

.tab-icon {
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 20rpx;
  color: #7A7E83;
  transition: color 0.3s ease;
}

.tab-text.active {
  color: #007AFF;
  font-weight: bold;
}
</style>
