<template>
  <view class="register-container">
    <view class="header">
      <text class="title">用户注册</text>
      <text class="subtitle">创建您的乾坤袋账号</text>
    </view>
    
    <view class="form-container">
      <view class="input-group">
        <text class="label">手机号码</text>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入手机号码" 
          v-model="form.phone"
          maxlength="11"
        />
      </view>
      
      <view class="input-group">
        <text class="label">验证码</text>
        <view class="verification-code">
          <input 
            class="input code-input" 
            type="number" 
            placeholder="请输入验证码" 
            v-model="form.verificationCode"
            maxlength="6"
          />
          <button 
            class="code-btn" 
            @click="getVerificationCode" 
            :disabled="countdown > 0"
          >
            {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
          </button>
        </view>
      </view>
      
      <view class="input-group">
        <text class="label">密码</text>
        <input 
          class="input" 
          type="password" 
          placeholder="请设置密码（6-20位字母、数字组合）" 
          v-model="form.password"
          password
        />
      </view>
      
      <view class="input-group">
        <text class="label">确认密码</text>
        <input 
          class="input" 
          type="password" 
          placeholder="请再次输入密码" 
          v-model="form.confirmPassword"
          password
        />
      </view>
      
      <view class="real-name-section">
        <text class="section-title">实名认证信息</text>
        
        <view class="input-group">
          <text class="label">真实姓名</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入您的真实姓名" 
            v-model="form.realName"
          />
        </view>
        
        <view class="input-group">
          <text class="label">身份证号</text>
          <input 
            class="input" 
            type="idcard" 
            placeholder="请输入您的身份证号码" 
            v-model="form.idCard"
            maxlength="18"
          />
        </view>
      </view>
      
      <view class="agreement">
        <checkbox :checked="agreeTerms" @click="agreeTerms = !agreeTerms" color="#007AFF" />
        <text class="agreement-text">我已阅读并同意</text>
        <text class="agreement-link" @click="showTerms">《用户协议》</text>
        <text class="agreement-text">和</text>
        <text class="agreement-link" @click="showPrivacy">《隐私政策》</text>
      </view>
      
      <button class="register-btn" @click="handleRegister" :disabled="isLoading || !agreeTerms">
        <text v-if="!isLoading">注册</text>
        <text v-else>注册中...</text>
      </button>
      
      <view class="login-link">
        <text>已有账号？</text>
        <text class="link" @click="navigateToLogin">立即登录</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        phone: '',
        verificationCode: '',
        password: '',
        confirmPassword: '',
        realName: '',
        idCard: ''
      },
      countdown: 0,
      agreeTerms: false,
      isLoading: false
    }
  },
  methods: {
    getVerificationCode() {
      // 验证手机号
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        });
        return;
      }
      
      if (!/^1\d{10}$/.test(this.form.phone)) {
        uni.showToast({
          title: '手机号码格式不正确',
          icon: 'none'
        });
        return;
      }
      
      // 发送验证码请求
      // 这里应该是实际的发送验证码API调用
      
      // 开始倒计时
      this.countdown = 60;
      const timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      
      uni.showToast({
        title: '验证码已发送',
        icon: 'success'
      });
    },
    
    handleRegister() {
      // 表单验证
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        });
        return;
      }
      
      if (!/^1\d{10}$/.test(this.form.phone)) {
        uni.showToast({
          title: '手机号码格式不正确',
          icon: 'none'
        });
        return;
      }
      
      if (!this.form.verificationCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        });
        return;
      }
      
      if (!this.form.password) {
        uni.showToast({
          title: '请设置密码',
          icon: 'none'
        });
        return;
      }
      
      if (this.form.password.length < 6 || this.form.password.length > 20) {
        uni.showToast({
          title: '密码长度应为6-20位',
          icon: 'none'
        });
        return;
      }
      
      if (this.form.password !== this.form.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        });
        return;
      }
      
      if (!this.form.realName) {
        uni.showToast({
          title: '请输入真实姓名',
          icon: 'none'
        });
        return;
      }
      
      if (!this.form.idCard) {
        uni.showToast({
          title: '请输入身份证号码',
          icon: 'none'
        });
        return;
      }
      
      // 身份证号码验证
      const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardReg.test(this.form.idCard)) {
        uni.showToast({
          title: '身份证号码格式不正确',
          icon: 'none'
        });
        return;
      }
      
      if (!this.agreeTerms) {
        uni.showToast({
          title: '请阅读并同意用户协议和隐私政策',
          icon: 'none'
        });
        return;
      }
      
      // 注册请求
      this.isLoading = true;
      
      // 模拟注册请求
      setTimeout(() => {
        // 这里应该是实际的注册API调用
        // 假设注册成功
        uni.setStorageSync('token', 'sample-token');
        uni.setStorageSync('userInfo', {
          id: '1',
          phone: this.form.phone,
          name: this.form.realName,
          isRealNameVerified: true
        });
        
        this.isLoading = false;
        
        uni.showToast({
          title: '注册成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 注册成功后跳转到扫描二维码页
            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/auth/scan-qr'
              });
            }, 2000);
          }
        });
      }, 1500);
    },
    
    showTerms() {
      uni.showModal({
        title: '用户协议',
        content: '这是用户协议内容...',
        showCancel: false
      });
    },
    
    showPrivacy() {
      uni.showModal({
        title: '隐私政策',
        content: '这是隐私政策内容...',
        showCancel: false
      });
    },
    
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/auth/login'
      });
    }
  }
}
</script>

<style>
.register-container {
  padding: 40rpx;
  background-color: #f8f8f8;
}

.header {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.input {
  height: 90rpx;
  border-bottom: 1px solid #e5e5e5;
  font-size: 32rpx;
  width: 100%;
}

.verification-code {
  display: flex;
  align-items: center;
}

.code-input {
  flex: 1;
}

.code-btn {
  width: 220rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 24rpx;
  background-color: #007AFF;
  color: #fff;
  border-radius: 35rpx;
  margin-left: 20rpx;
  padding: 0;
}

.real-name-section {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.agreement {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 40rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.agreement-link {
  font-size: 24rpx;
  color: #007AFF;
}

.register-btn {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #007AFF;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.login-link {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-top: 30rpx;
}

.link {
  color: #007AFF;
  margin-left: 10rpx;
}
</style>
