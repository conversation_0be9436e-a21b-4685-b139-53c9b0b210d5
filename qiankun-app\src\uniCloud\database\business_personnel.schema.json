{"bsonType": "object", "required": ["name", "phone"], "permission": {"read": true, "create": "auth.role == 'admin'", "update": "auth.role == 'admin'", "delete": "auth.role == 'admin'"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "name": {"bsonType": "string", "description": "业务人员姓名"}, "phone": {"bsonType": "string", "description": "业务人员手机号码", "pattern": "^1\\d{10}$"}, "department": {"bsonType": "string", "description": "所属部门"}, "qrCode": {"bsonType": "string", "description": "业务人员二维码URL"}, "createdAt": {"bsonType": "timestamp", "description": "创建时间", "defaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "description": "更新时间", "defaultValue": {"$env": "now"}}}}