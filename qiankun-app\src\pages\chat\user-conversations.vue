<template>
  <view class="conversations-container">
    <view class="page-header">
      <view class="header-content">
        <text class="header-title">{{ username }}的对话记录</text>
        <text class="header-subtitle">共 {{ conversations.length }} 条对话</text>
      </view>
    </view>
    
    <view class="conversation-list">
      <view 
        class="conversation-item" 
        v-for="(conversation, index) in conversations" 
        :key="index"
        @click="viewConversation(conversation)"
      >
        <view class="conversation-header">
          <text class="conversation-id">ID: {{ conversation.id.substring(0, 8) }}...</text>
          <text class="conversation-time">{{ formatDate(conversation.lastTime) }}</text>
        </view>
        
        <view class="conversation-content">
          <text class="conversation-message">{{ conversation.lastMessage }}</text>
        </view>
        
        <view class="conversation-footer">
          <text class="message-count">{{ conversation.messages.length }} 条消息</text>
          <view class="conversation-action">
            <text class="action-text">查看详情</text>
            <text class="action-icon">→</text>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-if="conversations.length === 0">
        <image class="empty-icon" src="/static/images/empty-chat.png"></image>
        <text class="empty-text">暂无对话记录</text>
        <text class="empty-subtext">该用户还没有进行过AI对话</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userId: '',
      username: '',
      conversations: []
    }
  },
  onLoad(options) {
    // 获取用户ID和用户名
    if (options.userId) {
      this.userId = options.userId;
    }
    
    if (options.username) {
      this.username = options.username;
    }
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: `${this.username}的对话`
    });
    
    // 加载该用户的对话记录
    this.loadUserConversations();
  },
  methods: {
    loadUserConversations() {
      // 从本地存储加载对话记录
      try {
        let allConversations = uni.getStorageSync('conversations') || [];
        
        // 过滤出该用户的对话
        this.conversations = allConversations.filter(c => c.userId === this.userId);
        
        // 按最后消息时间排序
        this.conversations.sort((a, b) => new Date(b.lastTime) - new Date(a.lastTime));
      } catch (error) {
        console.error('加载用户对话记录失败:', error);
        this.conversations = [];
      }
    },
    
    viewConversation(conversation) {
      // 跳转到对话详情页
      uni.navigateTo({
        url: `/pages/chat/chat-detail?id=${conversation.id}`
      });
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      
      // 如果是今天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return this.formatTime(date);
      }
      
      // 如果是昨天的消息，显示"昨天"
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + this.formatTime(date);
      }
      
      // 如果是今年的消息，显示月日
      if (date.getFullYear() === now.getFullYear()) {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
      
      // 其他情况显示完整日期
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },
    
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
  }
}
</script>

<style>
.conversations-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.page-header {
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
}

.header-content {
  display: flex;
  flex-direction: column;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: #999;
}

.conversation-list {
  flex: 1;
  padding: 20rpx;
}

.conversation-item {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.conversation-id {
  font-size: 24rpx;
  color: #999;
}

.conversation-time {
  font-size: 24rpx;
  color: #999;
}

.conversation-content {
  margin-bottom: 15rpx;
}

.conversation-message {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.conversation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f5f5f5;
  padding-top: 15rpx;
}

.message-count {
  font-size: 24rpx;
  color: #999;
}

.conversation-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 26rpx;
  color: #1890ff;
  margin-right: 10rpx;
}

.action-icon {
  font-size: 26rpx;
  color: #1890ff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
</style>
