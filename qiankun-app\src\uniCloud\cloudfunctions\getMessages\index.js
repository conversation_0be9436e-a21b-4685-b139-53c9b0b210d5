'use strict';

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const messagesCollection = db.collection('messages');
  
  // 获取请求参数
  const { userId, businessPersonnelId, page = 1, pageSize = 20 } = event;
  
  // 参数验证
  if (!userId || !businessPersonnelId) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 计算跳过的记录数
    const skip = (page - 1) * pageSize;
    
    // 查询消息
    const result = await messagesCollection
      .where({
        userId: userId,
        businessPersonnelId: businessPersonnelId
      })
      .orderBy('createdAt', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 获取总记录数
    const countResult = await messagesCollection
      .where({
        userId: userId,
        businessPersonnelId: businessPersonnelId
      })
      .count();
    
    const total = countResult.total;
    
    // 格式化消息数据
    const messages = result.data.map(msg => {
      return {
        id: msg._id,
        sender: msg.sender === 'user' ? 'self' : 'other', // 转换为前端使用的格式
        content: msg.content,
        time: formatTime(msg.createdAt),
        isRead: msg.isRead
      };
    });
    
    return {
      code: 200,
      message: '获取消息成功',
      data: {
        messages: messages.reverse(), // 反转顺序，使最新的消息在最后
        total,
        hasMore: total > skip + messages.length
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};

// 格式化时间的辅助函数
function formatTime(date) {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}
