<template>
  <view class="chat-list-container">
    <view class="business-personnel-info" v-if="businessPersonnel">
      <view class="bp-avatar">
        <text class="avatar-text">{{ businessPersonnel.name.substring(0, 1) }}</text>
      </view>
      <view class="bp-details">
        <text class="bp-name">{{ businessPersonnel.name }}</text>
        <text class="bp-department">{{ businessPersonnel.department }}</text>
      </view>
    </view>
    
    <view class="chat-list">
      <view 
        class="chat-item" 
        v-for="(chat, index) in chatList" 
        :key="index"
        @click="navigateToChatDetail(chat)"
      >
        <view class="chat-avatar" :class="{ 'business': chat.type === 'business' }">
          <text class="avatar-text">{{ chat.name.substring(0, 1) }}</text>
        </view>
        
        <view class="chat-content">
          <view class="chat-header">
            <text class="chat-name">{{ chat.name }}</text>
            <text class="chat-time">{{ chat.lastMessageTime }}</text>
          </view>
          
          <view class="chat-message">
            <text class="message-text">{{ chat.lastMessage }}</text>
            <view class="unread-badge" v-if="chat.unreadCount > 0">
              <text class="unread-count">{{ chat.unreadCount }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-if="chatList.length === 0">
        <image class="empty-icon" src="/static/images/empty-chat.png"></image>
        <text class="empty-text">暂无消息</text>
        <text class="empty-subtext">您可以向业务人员发起咨询</text>
        <button class="start-chat-btn" @click="startNewChat">发起咨询</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      businessPersonnel: null,
      chatList: []
    }
  },
  onLoad() {
    // 获取业务人员信息
    const businessPersonnel = uni.getStorageSync('businessPersonnel');
    if (businessPersonnel) {
      this.businessPersonnel = businessPersonnel;
    } else {
      // 未绑定业务人员，跳转到扫描二维码页
      uni.navigateTo({
        url: '/pages/auth/scan-qr'
      });
      return;
    }
    
    // 获取聊天列表
    this.getChatList();
  },
  onShow() {
    // 每次页面显示时刷新聊天列表
    this.getChatList();
  },
  methods: {
    getChatList() {
      // 这里应该是实际的获取聊天列表API调用
      // 模拟聊天列表数据
      setTimeout(() => {
        this.chatList = [
          {
            id: '1',
            type: 'business',
            name: this.businessPersonnel.name,
            lastMessage: '您好，有什么可以帮助您的吗？',
            lastMessageTime: '10:30',
            unreadCount: 2
          }
        ];
      }, 500);
    },
    
    navigateToChatDetail(chat) {
      uni.navigateTo({
        url: `/pages/chat/chat-detail?id=${chat.id}&name=${chat.name}&type=${chat.type}`
      });
    },
    
    startNewChat() {
      // 直接跳转到与业务人员的聊天页面
      uni.navigateTo({
        url: `/pages/chat/chat-detail?id=1&name=${this.businessPersonnel.name}&type=business`
      });
    }
  }
}
</script>

<style>
.chat-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.business-personnel-info {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.bp-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.avatar-text {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.bp-details {
  flex: 1;
}

.bp-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.bp-department {
  font-size: 24rpx;
  color: #666;
}

.chat-list {
  flex: 1;
  padding: 20rpx 0;
}

.chat-item {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
}

.chat-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #4CD964;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.chat-avatar.business {
  background-color: #007AFF;
}

.chat-content {
  flex: 1;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.chat-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.chat-time {
  font-size: 24rpx;
  color: #999;
}

.chat-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-badge {
  min-width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  background-color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
}

.unread-count {
  font-size: 24rpx;
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.start-chat-btn {
  background-color: #007AFF;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  padding: 0 60rpx;
  height: 90rpx;
  line-height: 90rpx;
}
</style>
