{"bsonType": "object", "required": ["phone", "password", "realName", "idCard"], "permission": {"read": true, "create": true, "update": "doc.owner == auth.uid", "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "phone": {"bsonType": "string", "description": "手机号码，用作登录账号", "pattern": "^1\\d{10}$"}, "password": {"bsonType": "string", "description": "密码，加密存储"}, "realName": {"bsonType": "string", "description": "真实姓名，实名认证信息"}, "idCard": {"bsonType": "string", "description": "身份证号码，实名认证信息", "pattern": "^(\\d{15}|\\d{18}|\\d{17}(\\d|X|x))$"}, "isRealNameVerified": {"bsonType": "bool", "description": "是否已实名认证", "defaultValue": false}, "businessPersonnelId": {"bsonType": "string", "description": "绑定的业务人员ID", "foreignKey": "business_personnel._id"}, "createdAt": {"bsonType": "timestamp", "description": "创建时间", "defaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "description": "更新时间", "defaultValue": {"$env": "now"}}}}