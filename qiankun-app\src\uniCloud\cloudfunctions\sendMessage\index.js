'use strict';

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const messagesCollection = db.collection('messages');
  
  // 获取请求参数
  const { userId, businessPersonnelId, content, sender } = event;
  
  // 参数验证
  if (!userId || !businessPersonnelId || !content || !sender) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  // 验证发送者类型
  if (sender !== 'user' && sender !== 'business') {
    return {
      code: 400,
      message: '发送者类型不正确'
    };
  }
  
  try {
    // 创建新消息
    const result = await messagesCollection.add({
      userId,
      businessPersonnelId,
      sender,
      content,
      isRead: false,
      createdAt: new Date()
    });
    
    return {
      code: 200,
      message: '发送消息成功',
      data: {
        messageId: result.id
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};
