/**
 * 认证相关工具类
 */

// 保存用户登录信息
export const saveUserInfo = (token, userInfo) => {
  uni.setStorageSync('token', token);
  uni.setStorageSync('userInfo', userInfo);
};

// 保存业务人员信息
export const saveBusinessPersonnel = (businessPersonnel) => {
  uni.setStorageSync('businessPersonnel', businessPersonnel);
};

// 获取用户信息
export const getUserInfo = () => {
  return uni.getStorageSync('userInfo') || null;
};

// 获取用户详细信息的各个字段
export const getUserDetail = () => {
  const userInfo = getUserInfo();
  if (!userInfo) return null;

  return {
    id: userInfo.id,
    username: userInfo.username,
    name: userInfo.name,
    level: userInfo.level,
    picture: userInfo.picture,
    notice: userInfo.notice,
    role: userInfo.role
  };
};

// 获取用户头像
export const getUserPicture = () => {
  const userInfo = getUserInfo();
  return userInfo?.picture || '';
};

// 获取用户通知
export const getUserNotice = () => {
  const userInfo = getUserInfo();
  return userInfo?.notice || '';
};

// 获取用户级别
export const getUserLevel = () => {
  const userInfo = getUserInfo();
  return userInfo?.level || 3;
};

// 获取用户角色名称
export const getUserRoleName = () => {
  const userInfo = getUserInfo();
  if (!userInfo) return '未知';

  switch (userInfo.role) {
    case 'admin':
      return '超级管理员';
    case 'manager':
      return '客户经理';
    case 'user':
      return '普通用户';
    default:
      return '未知';
  }
};

// 检查用户是否有管理权限
export const hasManagementPermission = () => {
  const userInfo = getUserInfo();
  return userInfo && (userInfo.role === 'admin' || userInfo.role === 'manager');
};

// 检查用户是否可以创建用户
export const canCreateUser = () => {
  const userInfo = getUserInfo();
  return userInfo && (userInfo.role === 'admin' || userInfo.role === 'manager');
};

// 检查用户是否可以访问用户管理页面
export const canAccessUserManagement = () => {
  return hasManagementPermission();
};

// 检查用户是否可以访问历史记录页面
export const canAccessChatHistory = () => {
  return hasManagementPermission();
};

// 清空所有本地缓存
export const clearAllCache = () => {
  try {
    console.log('开始清空本地缓存');

    // 清空token
    uni.removeStorageSync('token');
    console.log('已清空token');

    // 清空用户信息
    uni.removeStorageSync('userInfo');
    console.log('已清空用户信息');

    // 清空其他可能的缓存数据
    const cacheKeys = [
      'chatHistory',
      'messageHistory',
      'userSettings',
      'appConfig',
      'lastLoginTime'
    ];

    cacheKeys.forEach(key => {
      try {
        uni.removeStorageSync(key);
        console.log(`已清空缓存: ${key}`);
      } catch (error) {
        console.warn(`清空缓存失败: ${key}`, error);
      }
    });

    console.log('本地缓存清空完成');
    return true;
  } catch (error) {
    console.error('清空本地缓存失败:', error);
    return false;
  }
};

// 安全退出登录（调用接口 + 清空缓存）
export const safeLogout = async (store = null) => {
  try {
    console.log('开始安全退出登录');

    // 导入API方法
    const { userApi } = await import('@/utils/api.js');

    // 调用退出登录接口
    const response = await userApi.logout();
    console.log('退出登录接口响应:', response);

    // 无论接口是否成功，都要清空本地缓存
    const cacheCleared = clearAllCache();

    // 清空Vuex状态
    if (store) {
      try {
        store.commit('user/CLEAR_USER');
        console.log('已清空Vuex用户状态');
      } catch (error) {
        console.warn('清空Vuex状态失败:', error);
      }
    }

    if (response.code === 200 && cacheCleared) {
      console.log('安全退出登录成功');
      return { success: true, message: '退出登录成功' };
    } else {
      console.warn('退出登录部分失败，但已清空本地缓存');
      return { success: true, message: '退出登录成功' };
    }
  } catch (error) {
    console.error('安全退出登录失败:', error);

    // 即使出错也要清空本地缓存
    clearAllCache();

    // 清空Vuex状态
    if (store) {
      try {
        store.commit('user/CLEAR_USER');
        console.log('已清空Vuex用户状态（错误处理）');
      } catch (error) {
        console.warn('清空Vuex状态失败（错误处理）:', error);
      }
    }

    return { success: true, message: '退出登录成功' };
  }
};

// 获取业务人员信息
export const getBusinessPersonnel = () => {
  return uni.getStorageSync('businessPersonnel') || null;
};

// 获取 token
export const getToken = () => {
  return uni.getStorageSync('token') || '';
};

// 检查是否已登录
export const isLoggedIn = () => {
  return !!getToken();
};

// 检查是否已绑定业务人员
export const hasBusinessPersonnel = () => {
  return !!getBusinessPersonnel();
};

// 清除登录信息
export const clearLoginInfo = () => {
  uni.removeStorageSync('token');
  uni.removeStorageSync('userInfo');
};

// 清除所有认证信息
export const clearAllAuthInfo = () => {
  clearLoginInfo();
  uni.removeStorageSync('businessPersonnel');
};

// 检查是否已实名认证
export const isRealNameVerified = () => {
  const userInfo = getUserInfo();
  return userInfo && userInfo.isRealNameVerified;
};
