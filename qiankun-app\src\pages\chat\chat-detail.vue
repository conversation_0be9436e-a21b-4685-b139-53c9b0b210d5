<template>
  <view class="chat-detail-container">
    <scroll-view
      class="message-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
    >
      <view class="loading" v-if="isLoading">
        <text>加载中...</text>
      </view>

      <view
        class="message-item"
        v-for="(message, index) in conversation.messages"
        :key="index"
        :class="{ 'user': message.role === 'user', 'assistant': message.role === 'assistant' }"
      >
        <view class="message-avatar" :class="{ 'user': message.role === 'user' }">
          <text class="avatar-text">{{ message.role === 'user' ? conversation.username.substring(0, 1) : 'AI' }}</text>
        </view>

        <view class="message-content">
          <view class="message-bubble">
            <rich-text v-if="message.role === 'assistant'" :nodes="renderMarkdown(message.content)"></rich-text>
            <text v-else class="message-text">{{ message.content }}</text>
          </view>
          <text class="message-time">{{ message.time }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import marked from 'marked';

export default {
  data() {
    return {
      conversationId: '',
      conversation: {
        id: '',
        userId: '',
        username: '',
        messages: []
      },
      scrollTop: 0,
      isLoading: true
    }
  },
  onLoad(options) {
    // 获取会话ID
    this.conversationId = options.id;

    // 加载会话详情
    this.loadConversation();
  },
  methods: {
    loadConversation() {
      this.isLoading = true;

      // 从本地存储加载会话详情
      try {
        const conversations = uni.getStorageSync('conversations') || [];
        const conversation = conversations.find(c => c.id === this.conversationId);

        if (conversation) {
          this.conversation = conversation;

          // 设置导航栏标题
          uni.setNavigationBarTitle({
            title: `与 ${conversation.username} 的对话`
          });

          // 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        } else {
          uni.showToast({
            title: '未找到对话记录',
            icon: 'none'
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      } catch (error) {
        console.error('加载对话记录失败:', error);
        uni.showToast({
          title: '加载对话记录失败',
          icon: 'none'
        });
      }

      this.isLoading = false;
    },

    renderMarkdown(text) {
      try {
        // 使用marked库将Markdown转换为HTML
        const html = marked(text);
        return html;
      } catch (error) {
        console.error('Markdown渲染错误:', error);
        return text;
      }
    },

    scrollToBottom() {
      // 获取消息列表高度，滚动到底部
      const query = uni.createSelectorQuery().in(this);
      query.select('.message-list').boundingClientRect();
      query.exec(res => {
        if (res && res[0]) {
          this.scrollTop = res[0].height * 2; // 乘以2确保滚动到底部
        }
      });
    }
  }
}
</script>

<style>
.chat-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.message-list {
  flex: 1;
  padding: 20rpx;
}

.loading {
  text-align: center;
  padding: 20rpx 0;
}

.loading text {
  font-size: 24rpx;
  color: #999;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
  flex-shrink: 0;
}

.message-avatar.user {
  background-color: #4CD964;
}

.avatar-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user .message-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 20rpx;
  border-radius: 10rpx;
  background-color: #fff;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.user .message-bubble {
  background-color: #007AFF;
}

.message-text {
  font-size: 30rpx;
  color: #333;
}

.user .message-text {
  color: #fff;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}
</style>
