'use strict';

const crypto = require('crypto');

exports.main = async (event, context) => {
  // 获取数据库引用
  const db = uniCloud.database();
  const usersCollection = db.collection('users');
  
  // 获取请求参数
  const { phone, password } = event;
  
  // 参数验证
  if (!phone || !password) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 对密码进行加密
    const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');
    
    // 查询用户
    const result = await usersCollection.where({
      phone: phone,
      password: hashedPassword
    }).get();
    
    if (result.data.length === 0) {
      return {
        code: 401,
        message: '手机号或密码错误'
      };
    }
    
    const user = result.data[0];
    
    // 生成token
    const token = crypto.createHash('md5').update(user._id + Date.now().toString()).digest('hex');
    
    // 查询绑定的业务人员
    let businessPersonnel = null;
    if (user.businessPersonnelId) {
      const businessPersonnelResult = await db.collection('business_personnel').doc(user.businessPersonnelId).get();
      if (businessPersonnelResult.data.length > 0) {
        const bp = businessPersonnelResult.data[0];
        businessPersonnel = {
          id: bp._id,
          name: bp.name,
          department: bp.department,
          phone: bp.phone
        };
      }
    }
    
    return {
      code: 200,
      message: '登录成功',
      data: {
        token,
        userInfo: {
          id: user._id,
          phone: user.phone,
          name: user.realName,
          isRealNameVerified: user.isRealNameVerified
        },
        businessPersonnel
      }
    };
  } catch (error) {
    console.error(error);
    return {
      code: 500,
      message: '服务器错误'
    };
  }
};
