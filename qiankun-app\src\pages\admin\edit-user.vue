<template>
  <view class="edit-user-container">
    <view class="form-container">
      <view class="form-group">
        <text class="form-label">用户名</text>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入用户名" 
          v-model="form.username"
          disabled
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">姓名</text>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入姓名" 
          v-model="form.name"
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">重置密码</text>
        <input 
          class="form-input" 
          type="password" 
          placeholder="留空表示不修改密码" 
          v-model="form.password"
          password
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">确认密码</text>
        <input 
          class="form-input" 
          type="password" 
          placeholder="留空表示不修改密码" 
          v-model="form.confirmPassword"
          password
        />
      </view>
      
      <view class="form-group" v-if="userInfo.role === 'admin'">
        <text class="form-label">角色</text>
        <picker 
          mode="selector" 
          :range="roleOptions" 
          range-key="label" 
          @change="handleRoleChange"
          :value="selectedRoleIndex"
        >
          <view class="picker-view">
            <text>{{ roleOptions[selectedRoleIndex].label }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <button class="submit-btn" @click="handleSubmit" :disabled="isSubmitting">
        <text v-if="!isSubmitting">保存修改</text>
        <text v-else>保存中...</text>
      </button>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
  data() {
    return {
      userId: '',
      form: {
        username: '',
        name: '',
        password: '',
        confirmPassword: '',
        role: 'user'
      },
      roleOptions: [
        { value: 'user', label: '普通用户' },
        { value: 'manager', label: '客户经理' },
        { value: 'admin', label: '超级管理员' }
      ],
      selectedRoleIndex: 0,
      isSubmitting: false,
      isLoading: true
    }
  },
  computed: {
    ...mapState('user', ['userInfo'])
  },
  onLoad(options) {
    // 获取用户ID
    this.userId = options.id;
    
    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.redirectTo({
        url: '/pages/auth/login'
      });
      return;
    }
    
    // 检查用户权限
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo.role === 'user') {
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      
      return;
    }
    
    // 客户经理只能编辑普通用户
    if (userInfo.role === 'manager') {
      this.roleOptions = [
        { value: 'user', label: '普通用户' }
      ];
    }
    
    // 加载用户信息
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      this.isLoading = true;
      
      // 模拟加载用户信息
      setTimeout(() => {
        // 这里应该是实际的API调用
        
        // 模拟用户数据
        const userData = {
          id: this.userId,
          username: 'user' + this.userId,
          name: '用户' + this.userId,
          role: 'user'
        };
        
        this.form.username = userData.username;
        this.form.name = userData.name;
        this.form.role = userData.role;
        
        // 设置角色选择器的索引
        this.selectedRoleIndex = this.roleOptions.findIndex(option => option.value === userData.role);
        if (this.selectedRoleIndex === -1) {
          this.selectedRoleIndex = 0;
        }
        
        this.isLoading = false;
      }, 500);
    },
    
    handleRoleChange(e) {
      this.selectedRoleIndex = e.detail.value;
      this.form.role = this.roleOptions[this.selectedRoleIndex].value;
    },
    
    handleSubmit() {
      // 表单验证
      if (!this.form.name) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return;
      }
      
      // 如果输入了密码，则验证密码
      if (this.form.password) {
        if (this.form.password.length < 6) {
          uni.showToast({
            title: '密码长度不能少于6位',
            icon: 'none'
          });
          return;
        }
        
        if (this.form.password !== this.form.confirmPassword) {
          uni.showToast({
            title: '两次输入的密码不一致',
            icon: 'none'
          });
          return;
        }
      }
      
      // 提交表单
      this.isSubmitting = true;
      
      // 模拟更新用户
      setTimeout(() => {
        // 这里应该是实际的API调用
        
        this.isSubmitting = false;
        
        uni.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        });
      }, 1500);
    }
  }
}
</script>

<style>
.edit-user-container {
  padding: 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  height: 90rpx;
  border-bottom: 1px solid #e5e5e5;
  font-size: 32rpx;
  width: 100%;
}

.picker-view {
  height: 90rpx;
  line-height: 90rpx;
  border-bottom: 1px solid #e5e5e5;
  font-size: 32rpx;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.submit-btn {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #007AFF;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-top: 40rpx;
}
</style>
